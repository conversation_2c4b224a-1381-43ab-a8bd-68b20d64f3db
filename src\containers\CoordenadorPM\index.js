import React, {useState, useContext, useRef, useEffect} from 'react';
import {AppState} from 'react-native';
import {View, Text, StatusBar, ScrollView} from 'react-native';
import {useLocation} from '../../context/LocationContext';
import {useUserContext} from '../../context/AuthContext';
import {usePermission} from '../../context/LocationPermissionContext';
import ReactNativeForegroundService from '@supersami/rn-foreground-service';
import {Button} from 'react-native-elements';

import Header from '../../components/Header';
import Mapa from '../../components/Mapa';
import AsyncStorage from '@react-native-community/async-storage';

import {useNavigation} from '@react-navigation/native';
import BotaoPanico from '../../components/BotaoPanico';
import BotaoQr from '../../components/BotaoQr';
import BotaoListagem from '../../components/BotoesListagem';
import MapaArea from '../../components/MapaArea';

function CoordenadorPM() {
  const navigation = useNavigation();
  const {location} = useLocation();
  const {requestLocationPermission, findCoordinates} = usePermission();
  const appState = useRef(AppState.currentState);
  const [appStateVisible, setAppStateVisible] = useState(appState.current);
  const [nome, setNome] = useState();

  console.disableYellowBox = true;

  useEffect(() => {
    AppState.addEventListener('change', _handleAppStateChange);
    return () => {
      AppState.removeEventListener('change', _handleAppStateChange);
    };
  }, []);

  const _handleAppStateChange = nextAppState => {
    appState.current = nextAppState;
    setAppStateVisible(appState.current);
    //console.log("AppState", appState.current);

    if (appState.current == 'inactive' || appState.current == 'background') {
      console.log('if', appState.current);
      ReactNativeForegroundService.add_task(
        () => {
          findCoordinates();
        },
        {
          delay: 1000,
          onLoop: false,
          taskId: 'taskid',
          onError: e => console.log('Error logging:', e),
        },
      );
      ReactNativeForegroundService.start({
        id: 144,
        title: 'AIBA',
        message: 'O aplicativo está rodando em segundo plano!',
      });
    } else {
      console.log('else', appState.current);
      ReactNativeForegroundService.stop();
      ReactNativeForegroundService.remove_all_tasks();
    }
  };

  const logout = async () => {
    await AsyncStorage.removeItem('@Id');
    await AsyncStorage.removeItem('@Identificacao');
    navigation.reset({
      index: 0,
      routes: [{name: 'Login'}],
    });
  };

  useEffect(() => {
    (async () => {
      await requestLocationPermission();
      await findCoordinates();
    })();
  }, []);

  return (
    <View>
      <StatusBar barStyle={'light-content'} />
      <ScrollView contentInsetAdjustmentBehavior="automatic">
        <Header subtitle="CoordenadorPM" />
        <Mapa />
        <BotaoListagem id={'COORDPM'}/>
        <MapaArea />
        <Button onPress={logout} title="Sair" />
        {/* {location &&
            <Text>Localização atual: {location.latitude}, {location.longitude}</Text>
          }  */}
        {/* <BotaoPanico /> */}
      </ScrollView>
    </View>
  );
}

export default CoordenadorPM;
