import React, { useState, useEffect } from 'react';
import { 
    View, 
    Text,
    StyleSheet,
    TextInput,
    Image,
    TouchableOpacity,
} from 'react-native';
import { Icon } from 'react-native-elements';

import axios from 'axios'
import { URL_API } from '../../../services/urls'
import { useUserContext } from '../../../context/AuthContext';
import AsyncStorage from '@react-native-community/async-storage';

function ComponentesViatura({
    componentesEscolhidos,
    setComponentesEscolhidos,
    viaturaEscolhida,
    voltar
}) {
    const [componentes, setComponentes] = useState([])
    const [filterText, setFilterText] = useState('')
    const {user} = useUserContext()
    const [userPm, setUserPm] = useState(0)

    const maximo_viatura = 5

    console.log(componentesEscolhidos)

    useEffect(() => {
        const getUser = async () => {
            const usuario = await AsyncStorage.getItem("@Usuario")
            const data = {
                id: JSON.parse(usuario)
            }
            console.log(data)
            axios.post(URL_API+'todospm_unidade', data)
            .then(res=>{
                setComponentes(res.data)
            })
        }

        getUser();
        
      }, [])


      const escolherComponente = componente => {
        const isAlreadyChosen = componentesEscolhidos.indexOf(componente) > -1

        if(isAlreadyChosen) {
            setComponentesEscolhidos(prev => prev.filter(e => e != componente))
        } else {
            if(componentesEscolhidos.length >= maximo_viatura) {
                return false
            }else {
                setComponentesEscolhidos(prev => [...prev, componente])
            }
        }

      }

  return (
      <View>
          <Text style={{marginBottom: 20}}>Selecione os componentes da viatura</Text>
            <TextInput
                style={styles.input}
                onChangeText={(e) => setFilterText(e)}
                value={filterText}
                placeholder="Digite aqui"
                placeholderTextColor='gray'
            />
            <View style={styles.viaturas}>
        {
            componentes ?  
            componentes
            .filter(({ativo_inativo}) => ativo_inativo == "ATIVO")
            .filter(({nome}) => {
                return (
                    nome.toLowerCase().includes(filterText.toLowerCase())
                )
            })
            .map(componente => {
                const isChosen = componentesEscolhidos.indexOf(componente.id) > -1
            return(
                <TouchableOpacity
                onPress={() => {
                    escolherComponente(componente.id)
                }}
                >
                <View style={{...styles.viaturaCard, borderColor: isChosen ? "rgba(0, 118, 193, 1)" : "#fff" }}>
                    <View style={{width: 60, height: 62, borderRadius: 81 / 2}}>
                    <Image
                        style={styles.viaturaImagem}
                        //source={require(``)}
                        source={{
                        uri: 'https://d1icd6shlvmxi6.cloudfront.net/gsc/WOYAFH/12/8a/25/128a2587997f4540b8774d0c44e4ac90/images/detalhe_rota__andamento_/u5333.png?token=e792de8cc604760d04af1a13150e39a58e58bb2fa83e57392f51640245784558',
                        }}
                    />
                    </View>
                    <View>
                      <Text style={{fontWeight: 'bold', fontSize: 17, marginBottom: 0}}>{componente.nome}</Text>
                      <Text style={{fontSize: 12, marginBottom: 5, color: '#999'}}>Viatura #{viaturaEscolhida.id_viat}</Text>
                    </View>
                    <View>
                    <Icon
                        name='navigate-next'
                        type='material'
                        color='rgba(0, 118, 193, 1)'
                        style={{alignItems: 'flex-start'}}
                        size={25}
                    />

                    </View>
                </View>
                </TouchableOpacity>
            )
            })
        : 
            <View>
                <Text style={{fontWeight: 'bold', fontSize: 17, marginBottom: 0, marginTop: 15}}>Não há componentes cadastrados na sua unidade</Text>

                <TouchableOpacity 
                    style={styles.voltar}
                    onPress={() => {
                        voltar()
                    }}
                >
                    <Text style={{color: 'rgba(0, 118, 193, 1)', textAlign: 'center', fontWeight:'bold'}}>Voltar</Text>
                </TouchableOpacity>
            </View>
        }
        </View>
      </View>
  );
}


const styles = StyleSheet.create({
    input: {
      height: 50,
      width: '92%',
      borderWidth: 1,
      padding: 15,
      borderColor:'#fff',
      borderRadius: 30,
      elevation:2,
      backgroundColor: '#fff',
      color: '#000',
  },
  viaturas: {
    flex: 1,
  },
  viaturaCard: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    borderWidth: 1,
    borderColor:'#fff',
    backgroundColor: '#fff',
    color: '#000',
    width: '100%',
    marginTop: 15,
    borderRadius: 20,
    elevation: 1,
    height: 97,
  },
  viaturaImagem: {
    width: '100%',
    height: '100%',
    borderRadius: 103/2
  },
  voltar: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 30,
    width: '100%',
    marginTop:'5%',
    borderColor:'rgba(0, 118, 193, 1)',
    borderWidth: 2
  },
})

export default ComponentesViatura;