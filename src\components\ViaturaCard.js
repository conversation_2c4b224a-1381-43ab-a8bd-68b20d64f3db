import React, {useState, useContext, useRef, useEffect} from 'react';
import {AppState,Alert,Button, Modal,StyleSheet,Pressable, TouchableOpacity} from 'react-native';
// import { Picker } from '@react-native-community/picker';
import MapaFazenda from './MapaFazenda';
import axios from 'axios';
import {URL_API} from '../services/urls';
import {

  View,
  Text,
  StatusBar,
  ScrollView,
  TextInput,
  VirtualizedList,
  FlatList,
} from 'react-native';
import AsyncStorage from '@react-native-community/async-storage';

function ViaturaCard(props) {
  const submit=()=>{
    console.log("viat:",props.data.id_viat)
    console.log("police id:",props.police)
    console.log("date:", props.date)
    console.log("routeid:",props.route)

      axios.put(URL_API+"salvar_rota2",
        {
            id_viat : props.data.id_viat,
            responsavel : props.police,
            data_inicio : props.date,
            status : "aguardando",
            id : props.route
        }).then(res=>console.log("SUCCESSSSSS",res)).then(res=>props.close(false)).catch(err=>console.log(err))
        }

    return (
      <TouchableOpacity onPress={()=>submit()} style={{width:'100%',height:200}}>
        <View style={{width:'100%',height:"100%"}}>
        <View style={styles.main}>
        <View style={styles.card}>
          <View style={styles.row}>
            <MapaFazenda />
            <View style={{width:'50%'}}>
              <View style={styles.dados}>
                <Text style={styles.titulo}>Viatura # {props.data.id_viat }</Text>
                <View style={styles.row}>
                  <Text>Components: </Text>
                  <Text style={styles.dado}>
                    {' '}
                    {props.data.componentes_viatura?props.data.componentes_viatura:"----"}
                  </Text>
                </View>
                <View style={styles.row}>
                  <Text>Modelo: </Text>
                  <Text style={styles.dado}> {props.data.modelo_viat}</Text>
                </View>
                <View style={styles.row}>
                  <Text>Localizacao</Text>
                  {/* <Text style={styles.dado}>
                    {' '}
                    {item.nome_da_area}
                  </Text> */}
                </View>
              </View>
            </View>
          </View>
        </View>
        </View>
        </View>
        </TouchableOpacity>
    )
}
const styles = StyleSheet.create({
    header: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      
    },
    main: {
      
      flex: 1,
      backgroundColor: '#fff',
      alignItems: 'center',
      
    },
    input1:{
      width:'40%',
      margin:5,
    },    
    inputt:{
      width:'80%',
      borderWidth:1,
      margin:5,
    },
    centeredView: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      marginTop: 22
    },
    modalView: {
      width:"100%",
      height:'100%',
      margin: 20,
      backgroundColor: "white",
      borderRadius: 20,
      padding: 35,
      justifyContent:'space-evenly',
      alignItems: "center",
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2
      },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5
    },
    button: {
      borderRadius: 20,
      padding: 10,
      elevation: 2
    },
    buttonOpen: {
      backgroundColor: "#F194FF",
    },
    buttonClose: {
      backgroundColor: "#2196F3",
    },
    textStyle: {
      color: "white",
      fontWeight: "bold",
      textAlign: "center"
    },
    modalText: {
      marginBottom: 15,
      textAlign: "center"
    },
    info: {
      marginTop: 20,
      marginBottom: 20,
      textAlign: 'center',
      color: 'gray',
      width: '70%',
      alignSelf: 'center',
    },
    card: {
      flex: 1,
      height: 100,
      width: '95%',
      backgroundColor: '#fff',
      padding: 5,
      elevation: 2,
      borderRadius: 30,
      padding: 10,
      marginBottom: '5%',
    },
    row: {
      flexDirection: 'row',
      flexWrap:'wrap',
      alignItems:'center',
      marginTop:5
    },
    mapa: {
      borderWidth: 1,
      borderRadius: 30,
    },
    input: {
      flex: 1,
      height: 40,
      margin: 12,
      padding: 10,
      elevation: 2,
      borderRadius: 30,
      backgroundColor: '#fff',
      color: 'gray',
    },
    button: {
      backgroundColor: '#0076C1',
      borderRadius: 30,
      flex: 1,
      justifyContent: 'center',
    },
    textButton: {
      color: '#fff',
      textAlign: 'center',
      fontWeight: 'bold',
    },
    dados: {
      flex: 1,
      flexDirection: 'column',
      width:'100%',
      justifyContent: 'flex-start',
      marginLeft: '5%',
    },
    titulo: {
      textAlign: 'left',
      marginBottom: '6%',
      fontWeight: 'bold',
    },
    dado: {
      color: 'gray',
      fontSize: 12,
      marginTop: '1%',
      width: '60%',
    },
    icon: {
      marginTop: '20%',
      marginHorizontal: '5%',
    },
    cardDetalhes: {
      height: 30,
      borderTopColor: 'gray',
      borderTopWidth: 2,
      marginTop: '2%',
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
  });

export default ViaturaCard
