import React, { useState, useContext, useCallback, useEffect } from 'react';
import { AppState, TouchableOpacity, StyleSheet, Alert } from "react-native";
import {
  View,
  Text,
  StatusBar,
  ScrollView,
  TextInput
} from 'react-native';

import { Dimensions } from 'react-native';


import { useNavigation } from '@react-navigation/native';

import {Picker} from '@react-native-picker/picker';
import axios from 'axios';
import { URL_API } from '../../services/urls';

const window = Dimensions.get('window');

const width = window.width;


function EditarFazenda() {

  const navigation = useNavigation();
  const [optionsPol, setOptionPol] = useState([]);
  const [optionsAreas, setOptionAreas] = useState([]);
  const [area, setArea] = useState();
  const [responsavel, setResponsavel] = useState();
  const [modelo, setModelo] = useState();
  const [placa, setPlaca] = useState();

useEffect(()=>{
    axios.get(URL_API+'todos_policiais')
    .then((res)=>{
        const itensPol = res.data
        const optionsPolRequest = itensPol.map((item)=>({
            id:item.id,
            name: item.nome
        }))
      setOptionPol(optionsPolRequest)
    })
},[])

useEffect(()=>{
  axios.get(URL_API+'todas_areas')
  .then((res)=>{
      const itensAreas = res.data
      const optionsAreasRequest = itensAreas.map((item)=>({
          id:item.id,
          name: item.nome_da_area
      }))
    setOptionAreas(optionsAreasRequest)
  })
 },[])

  const handleEnviar = () =>{
      if(modelo == null || placa == null){
        Alert.alert('','Preencha todos os campos')
      }
      else{
        axios.post(URL_API+'salvar_viatura',{
            
        })
        setModelo('')
        setPlaca('')
      }
  }
  console.log(area)
  return (
    <View style={styles.main}>
      <StatusBar barStyle={'light-content'} />
        <ScrollView contentInsetAdjustmentBehavior="automatic">
            <View style={styles.numbers}>
                <View>
                    <View style={styles.numberOne}>
                        <Text style={styles.one}>1</Text>
                    </View>
                    <Text style={styles.info}>Informações</Text>
                </View>
                <View>
                    <View style={styles.line}>
                        <Text style={{color:'transparent'}}>sadd</Text>
                    </View>
                    <Text style={{color:'transparent'}}> sadadada</Text>
                </View>
                <View>
                    <View style={styles.numberTwo}>
                        <Text style={styles.one}>2</Text>
                    </View>
                    <Text style={styles.local}>Localização</Text>
                </View>
            </View>
            <View style={styles.mainText}>
                <Text style={styles.text}>Preencha todos os campos abaixo corretamente para finalizar o registro:</Text>
            </View>
            <View style={styles.hr}>
            </View>
            <View style={styles.forms}>
                <Text style={styles.label}>Área de Operação</Text>
                    <View style={styles.picker}>
                        <Picker
                            selectedValue={area}
                            style={styles.inputPicker}
                            itemStyle={{height: '100%', width: '100%'}}
                            onValueChange={(itemValue, itemIndex) =>
                                setArea(itemValue)
                            }>
                            {optionsAreas.map((item)=>
                                <Picker.Item label={item.name} value={item.id} />
                            )}
                        </Picker>
                    </View>
                <Text style={styles.label}>Municipio</Text>
                <TextInput
                    style={styles.input}
                    onChangeText={(e)=> setPlaca(e)}
                    value={placa}
                    placeholder="Digite aqui"
                    placeholderTextColor='gray'
                />
                <Text style={styles.label}>Latitude</Text>
                <TextInput
                    style={styles.input}
                    onChangeText={(e)=> setPlaca(e)}
                    value={placa}
                    placeholder="Digite aqui"
                    placeholderTextColor='gray'
                />
                <Text style={styles.label}>Longitude</Text>
                <TextInput
                    style={styles.input}
                    onChangeText={(e)=> setPlaca(e)}
                    value={placa}
                    placeholder="Digite aqui"
                    placeholderTextColor='gray'
                />
                <Text style={styles.label}>Ocorrência registrada fora da delegacia?</Text>
                <View style={styles.picker}>
                    <Picker
                        selectedValue={responsavel}
                        style={styles.inputPicker}
                        itemStyle={{height: '100%', width: '100%'}}
                        onValueChange={(itemValue, itemIndex) =>
                            setResponsavel(itemValue)
                        }>
                        <Picker.Item label='Sim' value={true} />
                        <Picker.Item label='Não' value={false} />
                    </Picker>
                </View>
            </View>
            <View>
                <TouchableOpacity style={styles.button} onPress={handleEnviar}>
                    <Text style={styles.textButton}>Finalizar</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.buttonVoltar}>
                    <Text style={styles.textButtonVoltar}>Voltar</Text>
                </TouchableOpacity>
            </View>
        </ScrollView>
    </View> 
  );
}

const styles = StyleSheet.create({
    main:{
        flex:1,
    },
    mainText:{
        flex:1,
        width:'80%',
        alignSelf:"center",
        marginTop:'5%',
    },
    text:{
        textAlign:'center',
        color:'gray',
    },
    hr:{
        width:'95%',
        borderTopColor:"gray",
        borderTopWidth:1,
        height:3,
        alignSelf:"center",
        marginTop:'3%'
    },
    input: {
        width: '92%',
        borderWidth: 1,
        padding: 10,
        borderColor:'#fff',
        borderRadius: 30,
        elevation:2,
        backgroundColor: '#fff',
        color: '#000'
    },
    inputPicker: {
        height: Platform.OS == 'ios' ? 65 : 45,
        width: '92%',
        borderWidth: 1,
        padding: 10,
        borderColor:'#fff',
        borderRadius: 30,
        backgroundColor: '#fff'
    },
    forms:{
        marginLeft:'6%',
        flex:1,
        justifyContent: 'space-between'
    },
    label:{
        fontSize: 18,
        marginBottom: '5%',
        marginTop: '5%',
        color:'#5D5D5D'
    },
    button:{
        alignSelf:'center',
        backgroundColor: 'rgba(0, 118, 193, 1)',
        padding:10,
        width:'70%',
        borderRadius:30,
        height:50,
        justifyContent:'center',
        marginTop: '12%'
    },
    textButton:{
        textAlign:'center',
        color:'#fff',
        fontSize:18
    },
    buttonVoltar:{
        alignSelf:'center',
        justifyContent:'center',
        marginTop: '8%'
    },
    textButtonVoltar:{
        color: 'rgba(0, 118, 193, 1)',
        textAlign: 'center',
        fontSize: 18
    },
    picker:{
        borderRadius: 30, 
        borderColor: '#fff', 
        overflow: 'hidden', 
        width:'92%',
        backgroundColor:'#fff',
        elevation:1
    },
    numbers:{
        flex:1,
        flexDirection:'row',
        justifyContent:'center',
        marginTop:'5%'
    },
    numberOne:{
        alignSelf:"center",
        backgroundColor:'#00A956',
        padding:10,
        borderRadius:100,
        width:'47%'
    },
    numberTwo:{
        alignSelf:"center",
        backgroundColor:'#0076C1',
        padding:10,
        borderRadius:100,
        width:'47%'
    },
    one:{
        color:"#fff",
        fontWeight:'bold',
        textAlign:'center'
    },
    line:{
        borderBottomColor:'#0076C1',
        borderBottomWidth:2,
        width:'200%',
        marginLeft:'-32%'
    },
    info:{
        marginTop:"4%",
        color:'#00A956',
        fontWeight:'bold'
    },
    local:{
        marginTop:"4%",
        color:'#0076C1',
        fontWeight:'bold'
    }
});

export default EditarFazenda;