import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ScrollView
} from 'react-native';
import MapboxGL from "@react-native-mapbox-gl/maps";
import { Dimensions } from 'react-native'
import { useLocation } from '../context/LocationContext';
import { useAlertContext } from '../context/AlertDadosContext';
import * as Progress from 'react-native-progress';
import { Icon } from 'react-native-elements'
import { Button } from 'react-native-elements/dist/buttons/Button';
import BotaoEnviarViatura from './BotaoEnviarViatura';
import BotaoSelectViatura from './BotaoSelectViatura';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

MapboxGL.setAccessToken("pk.eyJ1Ijoiam9pY3lhbGJ1cXVlcnF1ZSIsImEiOiJja3JueGdla2IybGo0Mm9wNnB3eW1qdzc2In0.ketsp_41i3caR8Y4rw_ZkQ");


function MapaDetalhe(latitude, longitude) {
  const [view, setView] = useState(false);
  useEffect(()=>{
    if(latitude.latitude != undefined && latitude.longitude != undefined){
      setView(true)
    }
  }, [])
    console.log('maoaaaaaaaa', latitude)
  return (
    <>
        <View style={styles.main}>
            <View style={styles.mapa}>
                {view 
                  ? 
                <MapboxGL.MapView
                    styleURL={MapboxGL.StyleURL.Street}
                    zoomLevel={10}
                    centerCoordinate={[Number(latitude.longitude), Number(latitude.latitude)]}
                    style={{flex: 1}}
                    logoEnabled={false}
                >
                    <MapboxGL.Camera
                    zoomLevel={10}
                    centerCoordinate={[Number(latitude.longitude), Number(latitude.latitude)]}
                    animationMode={'flyTo'}
                    animationDuration={0}
                    >
                    </MapboxGL.Camera>
                    
                    <MapboxGL.PointAnnotation
                      id={'markerFazenda'}
                      coordinate={[Number(latitude.longitude), Number(latitude.latitude)]}
                    ></MapboxGL.PointAnnotation>
                </MapboxGL.MapView>
                :
                <Progress.CircleSnail
                  style={styles.progressCircle}
                  color={['#31788A']}
                  size={50}
                  indeterminate={true}
                />
              }
            </View>
    
        </View>
      
    </>
  );
}

const styles = StyleSheet.create({
  main: {
    flex:1,
    alignItems:'center'
  },
  titulo:{
    textAlign:'left',
    marginLeft: '5%',
    marginTop:'4%',
    fontSize: 15,
    marginBottom:'5%'
  },
  mapa:{
    backgroundColor: '#fff',
    width: '90%',
    height:350,
    borderRadius: 30,
    overflow: 'hidden',
    marginTop:'10%'
  },
  mapaContent:{
    
  },
  progressCircle: {
    alignSelf: 'center',
  },
  
});

export default MapaDetalhe;