import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, Text, Alert, ActivityIndicator } from 'react-native'

import axios from 'axios';
import { useNavigation } from '@react-navigation/core';
import { Dimensions } from 'react-native';
import AsyncStorage from '@react-native-community/async-storage';
import { URL_SOCKET } from '../services/urls'

import { Icon } from 'react-native-elements';
import BotaoPanico from './BotaoPanico';
import { useUserContext } from '../context/AuthContext';
import BotaoEncerrarRota from './BotaoEncerrarRota';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

function BotaoListagem(props) {
  
  const navigation = useNavigation();
  
  return (
    <View style={styles.main}>
      <View style={styles.row}>
        {props.id == 'FZD'
          &&
          <>
            <BotaoPanico nomeFZD={props.nomeFZD} nomeArea={props.nomeAreaFZD}/>
            <TouchableOpacity style={styles.panicButton} onPress={()=> navigation.navigate('visitasFazenda',{id: props.nomeFZD})}>
              <Icon
                name='local-police'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Visitas Realizadas</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.panicButton} onPress={()=> navigation.navigate('ocorrencia',{id_fazenda: props.nomeFZD})}>
              <Icon
                name='add-circle'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Abrir Ocorrência</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.panicButton} onPress={()=> navigation.navigate('historicoOcorrencias',{id: props.nomeFZD})}>
              <Icon
                name='local-police'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Histórico de Ocorrências</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.panicButton} onPress={() => navigation.navigate('enviarInformacao',{id_fazenda: props.nomeFZD})}>
              <Icon
                name='add-circle'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Enviar Informação</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.panicButton} onPress={() => navigation.navigate('equipamentos', {id: props.nomeFZD})}>
            <Icon
                name='add-circle'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Equipamentos</Text>
            </TouchableOpacity>

          </>
          }
          {props.id == 'COORDPM'
          &&
          <>
            <TouchableOpacity onPress={() => navigation.navigate('Rotas')} style={styles.panicButton}>
              <Icon
                name='pin-drop'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Listagem de Visitas</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.panicButton} onPress={() => navigation.navigate('Fazendas')}>
              <Icon
                name='house'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Fazendas Cadastradas</Text>
            </TouchableOpacity>
          </>
        }
        {props.id == 'PM'
          &&
          <>
            <TouchableOpacity onPress={() => navigation.navigate('visitas')} style={styles.panicButton}>
              <Icon
                name='pin-drop'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Próximas Visitas</Text>
            </TouchableOpacity>

            <TouchableOpacity onPress={() => navigation.navigate('ocorrencias')} style={styles.panicButton}>
              <Icon
                name='add-circle-outline'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Ocorrências</Text>
            </TouchableOpacity>

            <BotaoEncerrarRota/>

            <TouchableOpacity onPress={() => navigation.navigate('informacoes')} style={styles.panicButton}>
              <Icon
                name='add-circle-outline'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Informações</Text>
            </TouchableOpacity>
          </>
        }
      </View>
      <View style={styles.row}>
        {/* {props.id == 'FZD'
          &&
          <>
            <TouchableOpacity style={styles.panicButton} onPress={() => navigation.navigate('informacao')}>
              <Icon
                name='add-circle'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Registrar Informação</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.panicButton}>
              <Icon
                name='local-police'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Visitas Realizadas</Text>
            </TouchableOpacity>
          </>
          } */}
          {props.id == 'COORDPM'
            &&
          <>
            <TouchableOpacity style={styles.panicButton}>
              <Icon
                name='local-taxi'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Viaturas Cadastradas</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.panicButton}>
              <Icon
                name='face'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Policiais Cadastrados</Text>
            </TouchableOpacity>
          </>
        }
      </View>
     
    </View>

  );
}

const styles = StyleSheet.create({
  main: {
    flex: 1,
    alignItems: 'center',
  },
  row: {
    justifyContent:'space-between',
    paddingHorizontal:22,
    flexWrap:'wrap',
    flex: 1,
    flexDirection: 'row',
  },
  panicButton: {
    padding: 15,
    borderRadius: 30,
    marginTop: '2%',
    width: '47%',
    backgroundColor: '#fff',
    elevation: 1,
   
  },
  textButton: {
    color: '#000',
    fontSize: 15,
    textAlign: 'left',
  },
  icon: {
    alignItems: 'flex-start',
  },
  panicButtonDisabled:{
    padding: 15,
    borderRadius: 30,
    marginTop: '-15%',
    width: '47%',
    backgroundColor: 'gray',
    elevation: 1,
    // marginHorizontal: '2%'
  },
  textButtonDisabled:{
    color: '#fff',
    fontSize: 15,
    textAlign: 'left',
  }
});

export default BotaoListagem;