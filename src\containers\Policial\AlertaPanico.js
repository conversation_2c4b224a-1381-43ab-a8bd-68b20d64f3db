import React, { useEffect, useState } from 'react';
import AsyncStorage from '@react-native-community/async-storage';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import MapboxGL from "@react-native-mapbox-gl/maps";
import { Dimensions } from 'react-native'
import { useLocation } from '../../context/LocationContext';
import { useAlertContext } from '../../context/AlertDadosContext';
import * as Progress from 'react-native-progress';
import { Icon } from 'react-native-elements'
import Geocode from "react-geocode";
import BotaoTracarRota from '../../components/BotaoTracarRota';
import axios from 'axios';
import { URL_SOCKET } from '../../services/urls';
import {useSocketContext} from '../../context/socketContext';



// set Google Maps Geocoding API for purposes of quota management. Its optional but recommended.
Geocode.setApiKey("AIzaSyC19ZmD6Kns8nk1yR5iZoCxRx493QdmUbQ");

// set response language. Defaults to english.
Geocode.setLanguage("pt-br");

// set response region. Its optional.
// A Geocoding request with region=es (Spain) will return the Spanish city.
Geocode.setRegion("br");

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

MapboxGL.setAccessToken("pk.eyJ1Ijoiam9pY3lhbGJ1cXVlcnF1ZSIsImEiOiJja3JueGdla2IybGo0Mm9wNnB3eW1qdzc2In0.ketsp_41i3caR8Y4rw_ZkQ");


function AlertaPanico() {

  const { location } = useLocation();
  const [indice, setIndice] = useState(0);
  const [id_viatura_atual, setId_viatura_atual] = useState(0);
  const [view, setView] = useState(false);
  const [loading, setLoading] = useState(true);
  const [temp, setTemp] = useState([]);
  const [panico_mais_proximo, setPanico_mais_proximo] = useState({});
  const {socket} = useSocketContext();

  const getViatura = async () => {
    const viatura = await AsyncStorage.getItem('@Viatura');

    setId_viatura_atual(viatura)
  }

  getViatura()



  const checkAlert = () => {
    axios.get(URL_SOCKET+'todos_panicos')
        .then(async data => {
            const dados = data.data.filter(data=> data.STATUS == 0 || data.STATUS == 1)
            AsyncStorage.setItem('@panicos', JSON.stringify(dados))
            setPanico_mais_proximo(dados)
            setLoading(false)
            /* for(let i=0; i < data.data.length; i++){
                if(data.data[i].IDENTIFICACAO != "FD"){
                    setData(draft => {draft.push([data.data[i].NOME,'',data.data[i].ID,'acionado',data.data[i].LATITUDE,data.data[i].LONGITUDE])}
                    );
                }
                else{
                    setData(draft => {draft.push([data.data[i].NOME,data.data[i].ENDERECO,data.data[i].ID,'acionado',data.data[i].LATITUDE,data.data[i].LONGITUDE])}
                    );
                }
            } */
        }).catch(async err => {
            const panicos = await AsyncStorage.getItem('@panicos');
            setPanico_mais_proximo(JSON.parse(panicos))
            console.log('@panicos', panicos)
            setLoading(false)
            console.error('erro ocorrencias',JSON.stringify(err)); 
        })
  }

  useEffect(() => {
    checkAlert()
    check();
    
  }, [2000])


  const handleNext = () => {
    console.log("object")
    setIndice(prev => {
      if (prev >= panico_mais_proximo.length - 1) return prev
      else return prev + 1
    })
  }

  const handleBack = () => {
    console.log("object")
    setIndice(prev => {
      if (prev <= 0) return prev
      else return prev - 1
    })
  }

  const check = () => {
    if(panico_mais_proximo.length>0){
      
      if (temp.includes(panico_mais_proximo[indice]["ID"])) {
        return 1
      }
      return 0
    }
  }


  useEffect(() => {
    if (location.latitude != undefined && location.longitude != undefined) {
      setView(true)
    }
    Geocode.fromLatLng("48.8583701", "2.2922926").then(
      (response) => {
        const address = response.results[0].formatted_address;
        console.log(address);
      },
      (error) => {
        console.error(error);
      }
    );

  }, [location.latitude])
  //console.log('panico', panico_mais_proximo)
  return (
    // panico_mais_proximo ?
    //   <>
    //     <View style={styles.main}>

    //     </View>
    //     <View style={styles.list}>

    //       <View style={styles.mapaContent}>
    //         <View style={styles.cardAlert}>
    //           <View style={styles.mapa}>
    //             <View style={styles.headerMapa}>
    //               <Text style={styles.textHeaderMapa} >VOCÊ É A VIATURA MAIS PRÓXIMA</Text>
    //             </View>
    //             {view
    //               ?
    //               <MapboxGL.MapView
    //                 styleURL={MapboxGL.StyleURL.Street}
    //                 zoomLevel={10}
    //                 centerCoordinate={[location.longitude, location.latitude]}
    //                 style={{ flex: 1 }}
    //                 logoEnabled={false}
    //               >
    //                 <MapboxGL.Camera
    //                   zoomLevel={10}
    //                   centerCoordinate={[location.longitude, location.latitude]}
    //                   animationMode={'flyTo'}
    //                   animationDuration={0}
    //                 >
    //                 </MapboxGL.Camera>
    //                 <MapboxGL.PointAnnotation
    //                   id={JSON.stringify(panico_mais_proximo["ID"])}
    //                   coordinate={[location.longitude, location.latitude]}
    //                 >
    //                   {true
    //                     ?
    //                     <View style={styles.annotationContainer}>
    //                       <View style={styles.annotationFill}>
    //                       </View>
    //                     </View>
    //                     :
    //                     <View style={styles.annotationContainer}>
    //                       <View style={styles.annotationFillAndamento} />
    //                     </View>
    //                   }
    //                   <MapboxGL.Callout title={panico_mais_proximo["NOME_FAZENDA"]} />
    //                 </MapboxGL.PointAnnotation>
    //                 <MapboxGL.UserLocation />
    //               </MapboxGL.MapView>
    //               :
    //               <Progress.CircleSnail
    //                 style={styles.progressCircle}
    //                 color={['#31788A']}
    //                 size={50}
    //                 indeterminate={true}
    //               />
    //             }
    //           </View>
    //           <View style={styles.headerAlert}>
    //             <Text style={styles.nomeFazenda}>Fazenda: {panico_mais_proximo && panico_mais_proximo["NOME_FAZENDA"]}</Text>
    //             <Text style={styles.enderecoFzd}>Rua Lázaro de Souza, 382 - Belenzinho</Text>
    //           </View>
    //           <View
    //             style={{
    //               borderBottomColor: '#D8D8D8',
    //               borderBottomWidth: 1,
    //               marginHorizontal: 13
    //             }}
    //           />
    //           <View style={styles.contentAlert}>
    //             <View style={styles.contentAlertTempo}>
    //               <Text style={styles.textTempo}>00:03:21</Text>
    //               <Text style={styles.textTempoAc}>Tempo acionado</Text>
    //             </View>
    //             <View style={styles.contentAlertTempo}>
    //               <Text style={styles.textViatura}>42</Text>
    //               <Text style={styles.textTempoAc}>min do local</Text>
    //             </View>
    //             <View style={styles.contentAlertTempo}>
    //               <Text style={styles.textViatura}>1.3Km</Text>
    //               <Text style={styles.textTempoAc}>do local</  Text>
    //             </View>
    //           </View>
    //           <View style={styles.botoes}>
    //             <BotaoTracarRota sett={[temp, setTemp]} status={check()} data={panico_mais_proximo} />
    //           </View>
    //         </View>
    //       </View>

    //     </View>

    //   </>
    //   :
      panico_mais_proximo.length != 0 && !loading ?
        <>
          <View style={styles.main}>

          </View>
          <View style={styles.list}>

            <View style={styles.mapaContent}>
              <View style={styles.cardAlert}>
                <View style={styles.mapa}>
                  <View style={styles.headerMapa}>
                    <TouchableOpacity style={styles.icon} onPress={() => handleBack()}>
                      <Icon
                        name='chevron-back-outline'
                        type='ionicon'
                        color='#DEA7A7'
                      />
                    </TouchableOpacity>
                    <Text style={styles.textHeaderMapa} >BOTÕES DE PÂNICO ACIONADO ({panico_mais_proximo.length})</Text>
                    <TouchableOpacity style={styles.icon} onPress={() => handleNext()}>
                      <Icon
                        name='chevron-forward-outline'
                        type='ionicon'
                        color='#DEA7A7'
                      />
                    </TouchableOpacity>
                  </View>
                  {view && panico_mais_proximo[indice]["LONGITUDE"] !== undefined
                    ?
                    <MapboxGL.MapView
                      styleURL={MapboxGL.StyleURL.Street}
                      zoomLevel={10}
                      centerCoordinate={[Number(panico_mais_proximo[indice]["LONGITUDE"]), Number(panico_mais_proximo[indice]["LATITUDE"])]}
                      style={{ flex: 1 }}
                      logoEnabled={false}
                    >
                      <MapboxGL.Camera
                        zoomLevel={10}
                        centerCoordinate={[Number(panico_mais_proximo[indice]["LONGITUDE"]), Number(panico_mais_proximo[indice]["LATITUDE"])]}
                        animationMode={'flyTo'}
                        animationDuration={0}
                      >
                      </MapboxGL.Camera>
                      <MapboxGL.PointAnnotation
                        id={JSON.stringify(panico_mais_proximo[indice]["ID"])}
                        coordinate={[Number(panico_mais_proximo[indice]["LONGITUDE"]), Number(panico_mais_proximo[indice]["LATITUDE"])]}
                      >
                        {true
                          ?
                          <View style={styles.annotationContainer}>
                            <View style={styles.annotationFill}>
                            </View>
                          </View>
                          :
                          <View style={styles.annotationContainer}>
                            <View style={styles.annotationFillAndamento} />
                          </View>
                        }
                        <MapboxGL.Callout title={panico_mais_proximo[indice]["NOME_FAZENDA"]} />
                      </MapboxGL.PointAnnotation>
                      <MapboxGL.UserLocation />
                    </MapboxGL.MapView>
                    :
                    <Progress.CircleSnail
                      style={styles.progressCircle}
                      color={['#31788A']}
                      size={50}
                      indeterminate={true}
                    />
                  }
                </View>
                <View style={styles.headerAlert}>
                  <Text style={styles.nomeFazenda}>Fazenda: {panico_mais_proximo[indice] && panico_mais_proximo[indice]["NOME_FAZENDA"]}</Text>
                  {/* <Text style={styles.enderecoFzd}>Rua Lázaro de Souza, 382 - Belenzinho</Text> */}
                </View>
                {/* <hr/> */}
                <View
                  style={{
                    borderBottomColor: '#D8D8D8',
                    borderBottomWidth: 1,
                    marginHorizontal: 13
                  }}
                />
                {/* <View style={styles.contentAlert}>
                  <View style={styles.contentAlertTempo}>
                    <Text style={styles.textTempo}>00:03:21</Text>
                    <Text style={styles.textTempoAc}>Tempo acionado</Text>
                  </View>
                  <View style={styles.contentAlertTempo}>
                    <Text style={styles.textViatura}>3</Text>
                    <Text style={styles.textTempoAc}>Viaturas próximas</Text>
                  </View>
                  <View style={styles.contentAlertTempo}>
                    <Text style={styles.textViatura}>1.3Km</Text>
                    <Text style={styles.textTempoAc}>do veiculo próximo</  Text>
                  </View>
                </View> */}
                <View style={styles.botoes}>
                  <BotaoTracarRota sett={[temp, setTemp]} status={check()} data={panico_mais_proximo[indice]} />
                </View>
              </View>
            </View>

          </View>
        </>
        :
        <>
        </>
  );
}

const styles = StyleSheet.create({
  main: {
    backgroundColor: 'rgba(0, 118, 193, 1)',
    padding: 15,
    height: 200,
    borderBottomLeftRadius: 50,
    borderBottomRightRadius: 50,
    flex: 1
  },
  mapa: {
    backgroundColor: '#fff',
    width: width - 50,
    height: 200,
    borderRadius: 30,
    borderBottomLeftRadius: 0,
    borderBottomEndRadius: 0,
    overflow: 'hidden',
  },
  mapaContent: {
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: 20,
    marginTop: -200,
    // height: 600
  },
  progressCircle: {
    alignSelf: 'center',
  },
  annotationContainer: {
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 0
  },
  annotationFill: {
    width: 30,
    height: 30,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 0,
    backgroundColor: '#b94343',
    transform: [{ scale: 0.8 }],
  },
  annotationFillAndamento: {
    width: 30,
    height: 30,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 0,
    backgroundColor: '#FFC107',
    transform: [{ scale: 0.8 }],
  },
  textHeaderMapa: {
    backgroundColor: '#b94343',
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
    marginTop: 3
  },
  headerMapa: {
    backgroundColor: '#B94343',
    color: '#fff',
    padding: 10,
    fontWeight: 'bold',
    fontSize: 12,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  cardAlert: {
    backgroundColor: '#fff',
    height: 400,
    borderRadius: 30,
    elevation: 2,
  },
  headerAlert: {
    flex: 1,
    justifyContent: 'center',
    flexDirection: 'column'
  },
  nomeFazenda: {
    textAlign: 'center',
    fontSize: 18,
    width: 300
  },
  enderecoFzd: {
    textAlign: 'center',
    fontSize: 13,
    color: 'gray'
  },
  contentAlert: {
    // flex: 1,
    flexDirection: 'row',
    justifyContent: 'center'
  },
  contentAlertTempo: {
    flex: 1,
    justifyContent: 'center'
  },
  textTempo: {
    color: '#b94343',
    fontSize: 17,
    textAlign: 'center'
  },
  textTempoAc: {
    color: 'gray',
    textAlign: 'center'
  },
  textViatura: {
    fontSize: 17,
    textAlign: 'center'
  },
  botoes: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center'
  },
  list: {
    // flex: 1,
    flexDirection: 'row',
    marginLeft: '2%'
  },
  icon: {
    width: 30,
    backgroundColor: 'transparent'
  }
});

export default AlertaPanico;