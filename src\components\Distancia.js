// import axios from 'axios'
// import React,{useEffect,useState} from 'react'
// import { getDistance, convertDistance } from 'geolib';
// import { useLocation } from '../context/LocationContext';

// export const Distancia = (props) => {

//     const [distancia, setDistancia] = useState(1000000)
//     const {location} = useLocation();

//     useEffect(()=>{
//         const distance = getDistance(
//             { latitude: props.lat, longitude: props.lng },
//             { latitude: location.latitude, longitude: location.longitude },
//         );
//         if(distance<distancia){
//             const distanceKM = convertDistance(distance, 'km')
//             setDistancia(distanceKM)
//         }
//         console.log(distance)
            
//     },[])
    
//     return (
        
//         distancia
        
//     )
// }

