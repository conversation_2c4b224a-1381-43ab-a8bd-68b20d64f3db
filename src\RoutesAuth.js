import React, { useEffect, useState } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Login from './pages/Login';
import SMS from './pages/SMS';
import Fazendeiro from './containers/Fazendeiro'
import Policial from './containers/Policial'
import CoordenadorPM from './containers/CoordenadorPM'
import { UserProvider } from './context/AuthContext';
import BotaoQr from './components/BotaoQr';
import Fazendas from './pages/Fazendas';
import CadViatura from './containers/Policial/CadViatura';
import IniciarServico from './containers/Policial/IniciarServico';
import Ocorrencia from './containers/Fazendeiro/Ocorrencia';
import EnviarInformacoes from './containers/Fazendeiro/EnviarInformacoes';
import DetalheFazenda from './pages/DetalheFazenda';
import CadastraGerente from './containers/Fazendeiro/CadastraGerente';
import EditarFazenda from './containers/Fazendeiro/EditarFazenda';
import HistoricoOcorrencias from './containers/Fazendeiro/HistoricoOcorrencias';
import { useSocketContext } from './context/socketContext';
import Route from './pages/Route';
import Visitas from './containers/Policial/Visitas';
import { URL_API } from './services/urls';
import axios from 'axios';
import PushNotification from "react-native-push-notification";
import AsyncStorage from '@react-native-community/async-storage';
import io from 'socket.io-client'
import QrViatura from './components/QrViatura';
import TextNUpload from './components/TextNUpload';
import Equipamentos from './pages/Equipamentos'
import EncerrarRota from './components/EncerrarRota';
import DetailPage from './containers/Fazendeiro/DetailPage';
import VisitasFazenda from './containers/Fazendeiro/Visitas';
import ListaOcorrencias from './pages/ListaOcorrencias';
import DetalheOcorrencia from './pages/DetalheOcorrencia';
import DetalheEquipamento from './pages/DetalheEquipamento';
import ListaInformacoes from './pages/ListaInformacoes';
import DetalheInformacao from './pages/DetalheInformacao';
import EscolherViatura from './containers/Policial/IniciarServico/EscolherViatura';
import WebViewTermo from './containers/WebViewTermo';


const Stack = createNativeStackNavigator();

function AuthRoutes() {
  const { socket } = useSocketContext();
  const [notification, setNotification] = useState()
  const [Connection, setConnection] = useState()
  const [ident, setIdent] = useState()
  // const [connected, setConnected] = useState()

  const getIdent = async () => {
    const identificacao = await AsyncStorage.getItem('@Identificacao')
    setIdent(identificacao)
    socket.on("Notify", (ele) => {
      // console.log("HERE's tghe Elelelelel", ele)
      if (identificacao == '"PM"' || identificacao == '"COORDPM"') {
        PushNotification.localNotification({
          title: 'Novo Alerta Acionado!',
          message: `Um novo alerta foi acionado!`,
          bigText: 'Verifique a localização no aplicativo',
          channelId: 'testing',
        })
      }
    })
  }

  useEffect(() => {
    getIdent()
  }, [ident])

  PushNotification.createChannel(
    {
      channelId: 'testing',
      channelName: 'test channel'
    }
  )

  // useEffect(() => {

  // }, [socket]) 


  // useEffect(() => {
  //   const checkNotification = async () => {
  //     // await AsyncStorage.clear()
  //     if (notification) {
  //       // await AsyncStorage.removeItem('notifications');
  //       console.log("NOTIFICATIONS >>>>>>>>>>>>>>>>>>>>>>>>: ", notification);
  //       if (await AsyncStorage.getItem("notifications")) {
  //         console.log('--------------------I GOT IT FROM THE STORAGE---------------')
  //         const present = await AsyncStorage.getItem("notifications")
  //         if (JSON.stringify(notification) !== present) {
  //           console.log('--------------------I SET THE NEW NOTIFICATION BROOOOOOOOO---------------')
  //           await AsyncStorage.setItem("notifications", JSON.stringify(notification))
  //         } else {
  //           console.log('--------------------I AM USING THE SAMEEE NOTIFICATIONNNN---------------')
  //           PushNotification.localNotification({
  //             title: 'Novo alerta acionado',
  //             message: 'Novo alerta acionado na fazenda {farm name}',
  //             bigText: 'La Casa De Papel baby',
  //             channelId: 'testing',
  //           })
  //         }
  //       } else {
  //         console.log('--------------------I DID NOT GET IT FROM THE STORAGE SO I WILL SET IT NOW---------------')
  //         PushNotification.localNotification({
  //           title: 'Novo alerta acionado',
  //           message: 'Novo alerta acionado na fazenda {farm name}',
  //           bigText: 'La Casa De Papel baby',
  //           channelId: 'testing',
  //         })
  //         await AsyncStorage.setItem("notifications", JSON.stringify(notification));
  //       }
  //     }
  //   }

  //   checkNotification();

  // }, [notification])

  return (
    <Stack.Navigator>
      <Stack.Screen options={{ headerShown: false }} name="Login" component={Login} />
      <Stack.Screen options={{ headerShown: false }} name="SMS" component={SMS} />
      <Stack.Screen options={{ headerShown: false }} name="Coord" component={CoordenadorPM} />
      <Stack.Screen options={{ headerShown: false }} name="Policial" component={Policial} />
      <Stack.Screen options={{ headerShown: false }} name="Fazendeiro" component={Fazendeiro} />
      <Stack.Screen options={{ headerShown: false }} name="Details" component={DetailPage} />

      <Stack.Screen options={{ headerShown: false }} name="WebViewTermo" component={WebViewTermo} />

      {/* <Stack.Screen options={{ headerShown: false }} name="enviarInformacao" component={EnviarInformacao} /> */}

      <Stack.Screen
        options={{
          title: 'Ler QR Code da fazenda',
          headerStyle: {
            backgroundColor: 'rgba(0, 118, 193, 1)',
          },
          headerTintColor: '#fff',
          headerShadowVisible: false
        }}
        name="Qr"
        component={BotaoQr}
      />
      <Stack.Screen
        options={{
          title: 'Ler QR Code da Fazenda',
          headerStyle: {
            backgroundColor: 'rgba(0, 118, 193, 1)',
          },
          headerTintColor: '#fff',
          headerShadowVisible: false
        }}
        name="Qr2"
        component={QrViatura}
      />

      <Stack.Screen
        options={{
          title: 'Arigatodd',
          headerStyle: {
            backgroundColor: 'rgba(0, 118, 193, 1)',
          },
          headerTintColor: '#fff',
          headerShadowVisible: false
        }}
        name="nop"
        component={TextNUpload}
      />
      {/* <Stack.Screen options={{ headerShown: false}} name="test" component={Test} /> */}
      {/* <Stack.Screen options={{ headerShown: false}} name="Rotas" component={Route} /> */}
      <Stack.Screen
        options={{
          title: '',
          headerStyle: {
            backgroundColor: 'rgba(0, 118, 193, 1)',
          },
          headerTintColor: '#fff',
          headerShadowVisible: false
        }}
        name="Fazendas"
        component={Fazendas}
      />

      <Stack.Screen
        options={{
          title: '',
          headerStyle: {
            backgroundColor: 'rgba(0, 118, 193, 1)',
          },
          headerTintColor: '#fff',
          headerShadowVisible: false
        }}
        name="equipamentos"
        component={Equipamentos}
      />

      <Stack.Screen
        options={{
          title: '',
          headerStyle: {
            backgroundColor: 'rgba(0, 118, 193, 1)',
          },
          headerTintColor: '#fff',
          headerShadowVisible: false
        }}
        name="detalheequipamento"
        component={DetalheEquipamento}
      />
      <Stack.Screen
        options={{
          title: '',
          headerStyle: {
            backgroundColor: 'rgba(0, 118, 193, 1)',
          },
          headerTintColor: '#fff',
          headerShadowVisible: false
        }}
        name="Rotas"
        component={Route}
      />
      <Stack.Screen options={{
        title: 'Cadastro de Viaturas',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="CadViatura"
        component={CadViatura}
      />
      <Stack.Screen options={{
        title: 'Registrar Ocorrência',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="ocorrencia"
        component={Ocorrencia}
      />
      <Stack.Screen options={{
        title: 'Histórico de Ocorrências',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="historicoOcorrencias"
        component={HistoricoOcorrencias}
      />
      <Stack.Screen options={{
        title: 'Cadastre uma nova informação',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="enviarInformacao"
        component={EnviarInformacoes}
      />
      <Stack.Screen options={{
        title: 'Novo Gerente',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="cadGerente"
        component={CadastraGerente}
      />
      <Stack.Screen options={{
        title: 'Detalhes da Fazenda',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="detFazenda"
        component={DetalheFazenda}
      />
      <Stack.Screen
        options={{ headerShown: false }}
        name="IniciarServico"
        component={IniciarServico}
      />
      <Stack.Screen options={{
        title: 'Editar Fazenda',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="editFazenda"
        component={EditarFazenda}
      />
      <Stack.Screen options={{
        title: 'Detalhes da rota definida',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="visitas"
        component={Visitas}
      />
      {/*lista ocorrencias*/}
      <Stack.Screen options={{
        title: 'Lista de Ocorrências',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="ocorrencias"
        component={ListaOcorrencias}
      />
      {/*lista informaçoes*/}
      <Stack.Screen options={{
        title: 'Lista de Informações',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="informacoes"
        component={ListaInformacoes}
      />
      {/*Tela de Detalhes Da Ocorrência*/}
      <Stack.Screen options={{
        title: 'Detalhes da Ocorrência',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="detalheOcorrencia"
        component={DetalheOcorrencia}
      />
      {/*Tela de Detalhes Da Informação*/}
      <Stack.Screen options={{
        title: 'Detalhes da Informação',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="detalheInformacao"
        component={DetalheInformacao}
      />
      <Stack.Screen options={{
        title: 'Encerrar Rota',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="encerrarRota"
        component={EncerrarRota}
      />
      <Stack.Screen options={{
        title: 'Visitas',
        headerStyle: {
          backgroundColor: 'rgba(0, 118, 193, 1)',
        },
        headerTintColor: '#fff',
        headerShadowVisible: false
      }}
        name="visitasFazenda"
        component={VisitasFazenda}
      />
    </Stack.Navigator>
  )
};

export default AuthRoutes;