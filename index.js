/**
 * @format
 */
import ReactNativeForegroundService from "@supersami/rn-foreground-service";
import {AppRegistry} from 'react-native';
import App from './src/App';
import {name as appName} from './app.json';
import PushNotification from "react-native-push-notification";

PushNotification.configure({
    onNotification:(notification)=>{
        console.log("WE NOTIFIED YOU:- ",notification)
    },
    requestPermissions: Platform.OS === 'ios'
})


ReactNativeForegroundService.register();
AppRegistry.registerComponent(appName, () => App);
