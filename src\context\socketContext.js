import React, { useEffect } from 'react';
import io from 'socket.io-client'
import { URL_SOCKET } from '../services/urls';

const SocketContext = React.createContext();

export function useSocketContext() {
    return React.useContext(SocketContext);
}

export function SocketProvider({children}){
        const SERVER = URL_SOCKET
        const socket = io(SERVER ,{      
            transports: ['websocket']});
        socket.connect();
       
    useEffect(()=>{
        socket.on('connect' , () => {
            console.log('now connected to socket server'); 
        })
        
        // socket.on('connect_error', (err) => {
        //     // console.log("now connection error found",err)
        // })
      
          /*socket.on('disconnect', () => {
            console.log("Disconnected Socket!")
          })*/
        console.log("hello socket 6"  , socket)
    },[])
    // const socket = React.useMemo(() => {
    //     return socketio(SERVER)
    //   }, []);

    return (
        <SocketContext.Provider value={{ socket }}>
            {children}
        </SocketContext.Provider>
    )
}


