import 'react-native';
import handleEnviar from '../src/containers/Fazendeiro/OcorrenciaHandleEnviar';


it('Teste handleEnviar Sucesso', async () => {
  const navigation = {
    goBack: () => {}
  }

  const axios = {
    post: async () => {
      return 200
    }
  }

  const idProprietario = 1;
  const motivo = 2;
  const descricao = 'Roubo a mão armada'
  const delegacia = 0;
  const id_fazenda = 1;

  const retorno = await handleEnviar(axios, navigation, motivo, descricao, delegacia, idProprietario, id_fazenda)
  expect(200).toBe(200);

});

//Erro passed
it('Teste handleEnviar Error', async () => {
  const navigation = {
    goBack: () => {}
  }

  const axios = {
    post: async () => {
      return 400
    }
  }

  const idProprietario = 1;
  const motivo = '';
  const descricao = ''
  const delegacia = 0;
  const id_fazenda = 1;

  const retorno = await handleEnviar(axios, navigation, motivo, descricao, delegacia, idProprietario, id_fazenda)
  expect(retorno).toBe(undefined);
  
});