import React, { useState, useContext, useRef, useEffect } from 'react';
import { AppState, StyleSheet, TouchableOpacity } from "react-native";
import {
  View,
  Text,
  StatusBar,
  ScrollView,
  TextInput,
  VirtualizedList,
  FlatList
} from 'react-native';

import { useNavigation } from '@react-navigation/native';

import { Icon } from 'react-native-elements';

import { Dimensions } from 'react-native'
import axios from 'axios';
import { URL_API } from '../services/urls';
import Gerentes from '../containers/Fazendeiro/Gerentes';
import MapaDetalhe from '../components/MapaDetalheFazenda';
import Informacoes from '../components/Informacoes';
import QrFazenda from '../components/QrFazenda';
import AsyncStorage from '@react-native-community/async-storage';
import {useUserContext} from '../context/AuthContext';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;


function DetalheFazenda() {

  const navigation = useNavigation();
  const {user} = useUserContext();

  const [telaTab, setTelaTab] = useState('info');
  const [fazendas, setFazendas] = useState({});
  const [dataCriada, setDataCriada] = useState();
  const [id, setId] = useState();
  const [erro, setErro] = useState(false)

  const getId = async () => {
    const idAsync = await AsyncStorage.getItem('@Id');
    if(idAsync){
      setId(idAsync);
      console.log('idAsyncIF', idAsync)
    } 
    else{
      setId(user.ID_USUARIO);
      console.log('idAsyncElse', idAsync)
    }
  };
  useEffect(() => {
    getId();
  }, []);

  useEffect(()=>{
    axios.post(URL_API+'buscar_fazenda_proprietario',{id:1})
    .then(res=>{
        setFazendas(res.data[0])
        const now = new Date()
        const data = res.data[0].data_do_cadastro

        const dataSplit = data.split('/');
        const day = dataSplit[0]; // 30
        const month = dataSplit[1]; // 03
        const year = dataSplit[2]; // 2019

        const past = new Date(year, month, day);

        const diff = Math.abs(now.getTime() - past.getTime()); // Subtrai uma data pela outra
        const days = Math.ceil(diff / (1000 * 60 * 60 * 24));
        setDataCriada(days)

    })
    .catch(res=>
        setErro(true)
    )
  },[])
  return (
        <View style={styles.main}>
            <ScrollView>
                {erro
                    ?
                    <Text>Nenhma Fazenda Encontrada para esse usuário</Text>
                    :
                    <>
                    <View style={styles.header}>
                    <Text style={styles.textHeader}>{fazendas.nome_da_fazenda}</Text>
                </View>
                <View style={styles.card}>
                    <View style={styles.cardContent}>
                        <Icon
                            name='local-gas-station'
                            type='material'
                            color='#3F98D0'
                            style={styles.icon}
                            size={35}
                        />
                        <Text>Combustível</Text>
                    </View>
                    <View style={styles.cardContent}>
                        <Icon
                            name='tour'
                            type='material'
                            color='#3F98D0'
                            style={styles.icon}
                            size={35}
                        />
                        <Text>43 visitas</Text>
                    </View>
                    <View style={styles.cardContent}>
                        <Icon
                            name='event'
                            type='material'
                            color='#3F98D0'
                            style={styles.icon}
                            size={35}
                        />
                        <Text>{dataCriada} dia(s)</Text>
                    </View>
                </View>
                <View style={styles.botao}>
                    <TouchableOpacity style={styles.contentBotao} onPress={()=>navigation.navigate('editFazenda')}>
                        <Text style={styles.botaoText}>Editar Informações</Text>
                    </TouchableOpacity>
                </View>
                <View style={styles.div}>
                </View>
                <View>
                    <View style={styles.tab}>
                        {telaTab == 'info'
                            ?
                            <TouchableOpacity style={styles.contentBotaoTabSelected}>
                                <Text style={styles.botaoTextSelected}>Informações</Text>
                            </TouchableOpacity>
                            :
                            <TouchableOpacity style={styles.contentBotaoTab} onPress={()=>setTelaTab('info')}>
                                <Text style={styles.botaoText}>Informações</Text>
                            </TouchableOpacity>
                        }
                        {telaTab == 'visit'
                            ?
                            <TouchableOpacity style={styles.contentBotaoTabSelected}>
                                <Text style={styles.botaoTextSelected}>Visitas</Text>
                            </TouchableOpacity>
                            :
                            <TouchableOpacity style={styles.contentBotaoTab} onPress={()=>setTelaTab('visit')}>
                                <Text style={styles.botaoText}>Visitas</Text>
                            </TouchableOpacity>
                        }
                        {telaTab == 'qr'
                            ?
                            <TouchableOpacity style={styles.contentBotaoTabSelected}>
                                <Text style={styles.botaoTextSelected}>QR Code</Text>
                            </TouchableOpacity>
                            :
                            <TouchableOpacity style={styles.contentBotaoTab} onPress={()=>setTelaTab('qr')}>
                                <Text style={styles.botaoText}>QR Code</Text>
                            </TouchableOpacity>
                        }
                    </View>
                    {telaTab == 'info' 
                        &&
                        <Informacoes data={fazendas}/>
                    }
                    {telaTab == 'qr' 
                        &&
                        <QrFazenda/>
                    }
                </View>
                    </>
                }
            </ScrollView>
        </View>
  );
}
const styles = StyleSheet.create({
    main:{
        flex:1,
        marginTop:'5%'
    },
    header:{
        alignItems:'center'
    },
    textHeader:{
        fontSize:20,
        color:'gray',
        fontWeight:'bold'
    },
    card:{
        flexDirection:'row',
        marginTop:'10%',
        justifyContent:'space-evenly'
    },
    cardContent:{
        backgroundColor:'#fff',
        padding:15,
        borderRadius:15,
        borderColor:'#E6EAEC',
        borderWidth:2
    },
    botao:{
        alignSelf:'center',
        marginTop:'10%'
    },
    contentBotao:{
        borderColor:'#3F98D0',
        borderWidth:2,
        padding:17,
        borderRadius:30,
    },
    botaoText:{
        color:'#3F98D0'
    },
    contentBotaoTabSelected:{
        backgroundColor:'#3F98D0',
        borderColor:'#3F98D0',
        padding:5,
        borderRadius:30,
        borderWidth:2
    },
    botaoTextSelected:{
        color:'#fff'
    },
    div:{
        borderBottomColor:'#E4E9EB',
        borderBottomWidth:2,
        width:"95%",
        height:2,
        alignSelf:'center',
        marginTop:'5%'
    },
    tab:{
        justifyContent:'space-evenly',
        flexDirection:'row',
        marginTop:"5%"
    },
    contentBotaoTab:{
        borderColor:'#3F98D0',
        borderWidth:2,
        padding:5,
        borderRadius:30,
    },
    cardLoc:{
        backgroundColor:'#fff',
        width:'87%',
        alignSelf:'center',
        padding:15,
        elevation:1,
        borderRadius:30,
        marginTop:'-20%'
    },
    cardHeaderLoc:{
        flexDirection:'row'
    },
    titulo:{
        fontWeight:'bold'
    },
    endereco:{
        color: '#3F98D0',
        fontSize:13,
        fontWeight:'bold'
    },
    cardTitulo:{
        fontWeight:'bold'
    },
    cardContentTitulo:{
        fontSize:13,
        color:'gray'
    }
  });
export default DetalheFazenda;