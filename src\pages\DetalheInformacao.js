
 import { useNavigation } from '@react-navigation/native';
 import axios from 'axios';
  import React,{useState,useEffect} from 'react';
  import {
    Alert,
    Linking,
    SafeAreaView,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
  } from 'react-native';
 import { URL_API } from '../services/urls';
 import moment from 'moment';
 
 
  function DetalheInformacao ({route}) {
 
   const navigation = useNavigation();
   const[motivosInformacao, setMotivosInformacao] = useState([]);
 
   const {
    id_motivo,
    } = route.params.item

    const fetchMotivosInformacao = async ()=> {
      const response = await axios.get(URL_API + "motivos_informacao")
      
      setMotivosInformacao(response.data)
    }

    useEffect(() => {
      fetchMotivosInformacao();
    }, [])

   const motivoInformacao = motivosInformacao.find((motivo) => motivo.id === route.params.item.id_motivo)
 
   return (
     <SafeAreaView style={styles.main}>
       <StatusBar />
       <ScrollView>
         <View>
           <Text style={styles.info}>Confira os Detalhes da Informação</Text>
         </View>
               <View style={styles.card}>
                 <View style={styles.option}>
                   <Text>#{route.params.item.id_fzd}</Text>
                   <Text>Fazenda: {route.params.item.fazendaNome}</Text> 
                   <Text>Proprietário: {route.params.item.proprietarioNome}</Text>
                   <Text>Motivo: {motivoInformacao ? motivoInformacao.descricao : ''}</Text>
                   <Text>{moment(route.params.item.created_at).format('DD/MM/YYYY hh:mm:ss')}</Text>
                   <Text style={styles.position}>Informação:</Text>
                   <Text></Text>
                   <Text>{route.params.item.informacao}</Text>
                 </View>
               </View>
               <TouchableOpacity  style={styles.voltar} onPress={() => navigation.navigate('informacoes')}>
                 <Text style={styles.textVoltar} >Voltar</Text>
               </TouchableOpacity>      
       </ScrollView>
     </SafeAreaView>
   );
}
 
   const styles = StyleSheet.create({
    
     main:{
       flex:1,
       backgroundColor: '#fff'
     },
 
     info:{
         marginTop:'5%',
         marginBottom:'5%',
         textAlign: 'center',
         color: 'gray',
         width: '70%',
         alignSelf: 'center'
     },
     card:{
       backgroundColor: '#fff',
       width: '100%',
       height:'auto',
       borderRadius: 30,
       elevation: 1,
       marginBottom: '10%',
     },
     cardModal:{
       backgroundColor: '#fff',
       width: '100%',
       height:'auto',
       borderRadius: 30,
       elevation: 0,
       marginBottom: '10%',
     },
     option:{
       // backgroundColor:'#ddd',
       marginLeft:'5%',
       marginRight:'5%',
       marginTop:'5%',
       marginBottom:'5%',
       padding:10,
       borderRadius:5,
     },
     position:{
       alignSelf: 'center',
       marginTop:'5%',
     },
     voltar: {
       backgroundColor: '#fff',
       padding: 20,
       borderRadius: 30,
       width: '90%',
       borderColor:'rgba(0, 118, 193, 1)',
       borderWidth:2,
       marginBottom: '10%',
       alignSelf: 'center'
     },
     textVoltar:{
       color:'rgba(0, 118, 193, 1)', 
       textAlign: 'center', 
       fontWeight:'bold',
     },
     encerrarOcorrencia:{
       backgroundColor: 'rgba(0, 118, 193, 1)',
       padding: 20,
       borderRadius: 30,
       width: '90%',
       borderColor:'rgba(0, 118, 193, 1)',
       borderWidth:2,
       alignSelf: 'center'
     },
     textEncerrarOcorrencia:{
       color:'#fff', 
       textAlign: 'center', 
       fontWeight:'bold',
     },
     box:{
      width:'100%',
      height:'100%',
      backgroundColor:'rgba(0,0,0,0.5)',
      justifyContent: 'center',
      alignItems: 'center',
     },
     boxBody:{
       width:'80%',
       height:'80%',
       backgroundColor:'#fff',
       borderRadius: 30,
      
     },
     input: {
       width: '92%',
       borderWidth: 1,
       padding: 10,
       borderColor: '#fff',
       borderRadius: 30,
       elevation: 2,
       backgroundColor: '#fff',
       color: '#000',
       alignSelf:'center',
   },
 });
   
  
  export default DetalheInformacao;
  