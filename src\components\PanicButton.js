import React from 'react'
import { View, Text, TouchableOpacity } from 'react-native'
import MapaFazenda from './MapaFazenda'

function PanicButton() {
    return (
        <View style={{ overflow: 'hidden', backgroundColor: 'white', width: '90%', height: 520, alignItems: 'center', borderWidth: 1, margin: 5, borderRadius: 20 }}>
            <Text style={{ fontWeight: "700", fontSize: 15.5, width: '100%', color: "white", textAlign: 'center', lineHeight: 50, height: 50, backgroundColor: '#B94343' }}>
                PANIC BUTTON ACTIVATED
            </Text>
            <View style={{ width: '100%', height: 160, borderWidth: 1 }}>
                <MapaFazenda/>
            </View>
            <View style={{ width: '100%', height: 230, borderWidth: 1 }}>
                <View style={{ justifyContent: 'center', alignItems: 'center', height: 100, borderWidth: 1, borderBottomColor: "black" }}>
                    <Text style={{ fontSize: 20 }}>
                        Farm: Joy of Living
                    </Text>
                    <Text style={{ fontSize: 17, color: "#D7D7D7" }}>
                        <PERSON><PERSON>, 382 - <PERSON><PERSON>zinho
                    </Text>
                    <Text>

                    </Text>
                </View>
                <View style={{ justifyContent: 'space-evenly', alignItems: 'center', flexDirection: 'row', width: '100%', height: 130 }}>
                    <View style={{ justifyContent:'center',alignItems:'center', width: 80 }}>
                        <Text style={{fontSize:20}}>
                            00:03:21
                        </Text>
                        <Text style={{textAlign:'center'}}>
                            Tempo
                            acionado
                        </Text>
                    </View>
                    <View style={{ alignItems: 'center', width: 70 }}>
                        <Text style={{fontSize:20}}>
                            42
                        </Text>
                        <Text style={{textAlign:'center'}}>
                            min do local
                        </Text>
                    </View>
                    <View style={{ alignItems: 'center', width: 70 }}>
                        <Text style={{fontSize:20}}>
                            1.3km
                        </Text>
                        <Text style={{textAlign:'center'}}>
                            do local
                        </Text>
                    </View>
                </View >
            </View>
                <TouchableOpacity style={{width:'90%',borderRadius:40,height:55,borderWidth:1,backgroundColor:"#B94343",marginTop:10}}>
                    <Text style={{lineHeight:55,fontWeight:'700',textAlign:'center',color:'white'}}>
                        Tracar rota
                    </Text>
                </TouchableOpacity>
        </View>
    )
}

export default PanicButton
