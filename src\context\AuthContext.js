import React, { useState, useContext, useEffect } from 'react';
import axios from 'axios';
import AsyncStorage from '@react-native-community/async-storage';
import { useNavigation } from '@react-navigation/native';
import { Alert } from 'react-native';
import { URL_API } from '../services/urls';

const UserContext = React.createContext();

export function useUserContext() {
  return useContext(UserContext);
}

export function UserProvider({ children }) {
  const [user, setUser] = useState({});
  const [authenticaded, setAuthenticated] = useState(false);
  const [panicoAcionado, setPanicoAcionado] = useState(false);
  const [loading, setLoading] = useState(false);
  const [token, setToken] = useState();
  const [error, setError] = useState();
  const [count, setCount] = useState(1);
  const [bloqueado, setBloqueado] = useState(false);

  const navigation = useNavigation();


  const setDados = async (identificacao, nome, id, user) => {
    await AsyncStorage.setItem('@Identificacao', JSON.stringify(identificacao));
    // await AsyncStorage.setItem('@Identificacao', 'FZD');
    await AsyncStorage.setItem('@Nome', nome);
    await AsyncStorage.setItem('@Id', JSON.stringify(id));
    await AsyncStorage.setItem("@Usuario", JSON.stringify(user))

    console.log('Mostrar set dados id', id, JSON.stringify(id))
  }

  const handleCodigo = async (codigo) => {
    setLoading(true);
    const key = await AsyncStorage.getItem('@Token');
    console.log('chaveAuthProvider', key)
    await axios.post(URL_API + 'SMS_CHECKD', {
      code: codigo,
      key: key
    }).then(res => {
      console.log(res.data.message);
      setLoading(false);
      if (res.data.message === 'OK') {
        console.log('userAuthProvider', user);
        setDados(user.IDENTIFICACAO, user.NOME, user.ID_USUARIO, user.USUARIO);
        console.log('nomeAuth', user.NOME)
        const ident = user.IDENTIFICACAO
        switch (ident) {
          case 'PM':
            navigation.reset({
              index: 0,
              routes: [{ name: 'IniciarServico' }],
            });
            break;
          case 'COORDPM':
            navigation.reset({
              index: 0,
              routes: [{ name: 'Coord' }],
            });
            break;
          case 'FZD':
            navigation.reset({
              index: 0,
              routes: [{ name: 'Fazendeiro' }],
            });
            break;
          default:
            break;
        }
      }
    }).catch(res => {
      if (res == 'INVALID') {
        Alert.alert('Erro', 'Reinicie o aplicativo e tente novamente');
      }
    })
  }

  const handleUserLogin = (email, password, token) => {
    setLoading(true);
    axios.post(URL_API + 'autenticar', {
      usuario: email,
      senha: password,
      chave: token
    }).then( async res => {
      // console.log(res.data)
      switch (res.data.message) {
        case "INVALID":
          setCount(count + 1);
          if (count > 2) {
            //setError('Usuario bloqueado, reinicie o aplicativo e tente novamente');
            Alert.alert('Erro', 'Usuario bloqueado, reinicie o aplicativo e tente novamente');
            setBloqueado(true);
            setLoading(false)
            break;
          }
          else {
            //setError('Username e/ou Senha incorreto(s). '+ (3-count) +' Tentativa(s) Restante(s).');
            Alert.alert('Erro', 'Username e/ou Senha incorreto(s). ' + (3 - count) + ' Tentativa(s) Restante(s).');
            setLoading(false);
            break;
          }
        case "BLOCKED":
          Alert.alert('Erro', 'Usuario bloqueado, reinicie o aplicativo e tente novamente');
          setLoading(false);
          break;
        case "EXCEEDED":
          Alert.alert('Erro', 'Usuario bloqueado, reinicie o aplicativo e tente novamente');
          setLoading(false);
          break;
        case "EXPIRED":
          Alert.alert('Erro', 'Usuario bloqueado, reinicie o aplicativo e tente novamente');
          setLoading(false);
          break;
        default:
          await setDados(res.data.data_user.IDENTIFICACAO, res.data.data_user.NOME, res.data.data_user.ID_USUARIO, res.data.data_user.USUARIO);
          setUser(res.data.data_user);
          const ident = res.data.data_user.IDENTIFICACAO
          setLoading(false);
          if(res.data.data_user.TERMO_ACEITE){
            switch (ident) {
              case 'PM':
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'IniciarServico' }],
                });
                break;
              case 'COORDPM':
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Coord' }],
                });
                break;
              case 'FZD':
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Fazendeiro' }],
                });
                break;
              default:
                break;
            // axios.post(URL_API + 'sms', {
            //   number: res.data.data_user.TELEFONE,
            //   key: token
            // }).then(res => {
            //   // console.log(res.data);
            //   navigation.reset({
            //     index: 0,
            //     routes: [{ name: 'SMS' }],
            //   });
            // })
            }
          }else{
            navigation.reset({
              index: 0, 
              routes: [{ name: 'WebViewTermo' }],
            });
          }
      }
    }).catch(error => {
      Alert.alert('Erro', 'Erro no servidor'); 
      console.log(error);
      setLoading(false);
    })
  }

  return (
    <UserContext.Provider value={
      {
        user,
        handleUserLogin,
        setDados,
        panicoAcionado,
        loading,
        error,
        handleCodigo,
        bloqueado,
      }
    }
    >
      {children}
    </UserContext.Provider>
  );
}