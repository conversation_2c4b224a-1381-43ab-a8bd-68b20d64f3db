import React, { useState, useContext, useCallback, useEffect } from 'react';
import { AppState, TouchableOpacity, StyleSheet, Alert } from "react-native";
import {
    View,
    Text,
    StatusBar,
    ScrollView,
    TextInput
} from 'react-native';

import { Dimensions } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Picker } from '@react-native-picker/picker';
import axios from 'axios';
import { URL_API } from '../../services/urls';
import AsyncStorage from '@react-native-community/async-storage';
import OcorrenciaHandleEnviar from './OcorrenciaHandleEnviar';

const window = Dimensions.get('window');
const width = window.width;

function Ocorrencia() {

    const navigation = useNavigation();
    const [motivo, setMotivo] = useState();
    const [descricao, setDescricao] = useState();
    const [delegacia, setDelegacia] = useState(0);
    const [dataMotivos, setDataMotivos] = useState([]);
    const route = useRoute();
    const [idProprietario, setIdProprietario] = useState();

    const id_fazenda = route.params.id_fazenda

    const getId = async () => {
        const idAsync = await AsyncStorage.getItem('@Id');
        setIdProprietario(idAsync);  
    };

    useEffect(() => {
        getId();
    }, []);

    useEffect(()=> {
        axios.get(URL_API + 'motivos_ocorrencia')
            .then(res => {
                setDataMotivos(res.data.Motivos)
            })
            .catch(err => {
                console.log(err)
            })
    }, [])

   
    return (
        <View style={styles.main}>
            <StatusBar barStyle={'light-content'} />
            <ScrollView contentInsetAdjustmentBehavior="automatic">
                <View style={styles.mainText}>
                    <Text style={styles.text}>Preencha todos os campos abaixo corretamente para finalizar o registro:</Text>
                </View>
                <View style={styles.hr}>
                </View>
                <View style={styles.forms}>
                    <Text style={styles.label}>Motivo da Ocorrência</Text>
                    <View style={styles.picker}>
                        <Picker
                            selectedValue={motivo}
                            style={styles.inputPicker}
                            itemStyle={{ height: '100%', width: '100%' }}
                            onValueChange={(itemValue, itemIndex) =>
                                setMotivo(itemValue)
                            }>
                            <Picker.Item label='Selecione' value='' />
                            {
                                dataMotivos && dataMotivos.map((motivos) => {
                                    return(
                                        <Picker.Item label={motivos.descricao} value={motivos.id} />
                                    )  
                                })
                            }
                        </Picker>
                    </View>
                    <Text style={styles.label}>Descreva o ocorrido</Text>
                    <TextInput
                        style={styles.input}
                        onChangeText={(e) => setDescricao(e)}
                        value={descricao}
                        placeholder="Digite aqui"
                        placeholderTextColor='gray'
                        maxLength={255}
                        numberOfLines={6}
                        multiline
                    />
                    <Text style={styles.label}>Ocorrência registrada fora da delegacia?</Text>
                    <View style={styles.picker}>
                        <Picker
                            selectedValue={delegacia}
                            style={styles.inputPicker}
                            itemStyle={{ height: '100%', width: '100%' }}
                            onValueChange={(itemValue, itemIndex) =>
                                setDelegacia(itemValue)
                            }>
                            <Picker.Item label='Sim' value={0} />
                            <Picker.Item label='Não' value={1} />
                        </Picker>
                    </View>
                </View>
                <View>
                    <TouchableOpacity style={styles.button} onPress={() => OcorrenciaHandleEnviar(axios,
                        navigation, motivo, descricao, delegacia, idProprietario, id_fazenda)}>
                        <Text style={styles.textButton}>Finalizar</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={styles.buttonVoltar}
                        onPress={() => {
                            navigation.goBack()
                        }}
                    >
                        <Text style={styles.textButtonVoltar}>Voltar</Text>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </View>
    );
}

const styles = StyleSheet.create({
    main: {
        flex: 1,
    },
    mainText: {
        flex: 1,
        width: '80%',
        alignSelf: "center",
        marginTop: '5%',
    },
    text: {
        textAlign: 'center',
        color: 'gray',
    },
    hr: {
        width: '95%',
        borderTopColor: "gray",
        borderTopWidth: 1,
        height: 3,
        alignSelf: "center",
        marginTop: '3%'
    },
    input: {
        width: '92%',
        borderWidth: 1,
        padding: 10,
        borderColor: '#fff',
        borderRadius: 30,
        elevation: 2,
        backgroundColor: '#fff',
        color: '#000'
    },
    inputPicker: {
        height: Platform.OS == 'ios' ? 65 : 45,
        width: '92%',
        borderWidth: 1,
        padding: 10,
        borderColor: '#fff',
        borderRadius: 30,
        backgroundColor: '#fff'
    },
    forms: {
        marginLeft: '6%',
        flex: 1,
        justifyContent: 'space-between'
    },
    label: {
        fontSize: 18,
        marginBottom: '5%',
        marginTop: '5%',
        color: '#5D5D5D'
    },
    button: {
        alignSelf: 'center',
        backgroundColor: 'rgba(0, 118, 193, 1)',
        padding: 10,
        width: '70%',
        borderRadius: 30,
        height: 50,
        justifyContent: 'center',
        marginTop: '12%'
    },
    textButton: {
        textAlign: 'center',
        color: '#fff',
        fontSize: 18
    },
    buttonVoltar: {
        alignSelf: 'center',
        justifyContent: 'center',
        marginTop: '8%'
    },
    textButtonVoltar: {
        color: 'rgba(0, 118, 193, 1)',
        textAlign: 'center',
        fontSize: 18
    },
    picker: {
        borderRadius: 30,
        borderColor: '#fff',
        overflow: 'hidden',
        width: '92%',
        backgroundColor: '#fff',
        elevation: 1
    }
});

export default Ocorrencia;

export {
    OcorrenciaHandleEnviar
}