import React, { useState, useContext, useEffect } from 'react';
import { useLocation } from './LocationContext';
import {
  Alert,
  PermissionsAndroid,
  Platform
} from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import { useUserContext } from './AuthContext';
import { saveGeolocation } from '../api'
import AsyncStorage from '@react-native-community/async-storage';
import { check, PERMISSIONS, RESULTS, requestMultiple } from 'react-native-permissions';


const PermissionContext = React.createContext();

export function usePermission() {
  return useContext(PermissionContext);
}

export function PermissionProvider({ children }) {
    const { setLocation } = useLocation();
    const { user } = useUserContext();
    const [geolocationInterval, setGeolocationInterval] = useState(null);
    const[id, setId] = useState()

    async function getId(){
      const idAsync = await AsyncStorage.getItem('@Id');
      if(idAsync){
        setId(idAsync);
      }
      else{
        setId(user.ID_USUARIO)
      }
    }

    useEffect(() => {
      getId();
    },[id])
    
    const requestLocationPermission = async () => {
      if (Platform.OS == 'ios'){

        try {

          const granted = await requestMultiple([
            PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
            PERMISSIONS.IOS.LOCATION_ALWAYS,
          ])
          
        } catch (err) {
          console.warn(err);
        }

      } else {
        try {
          const grantedBackground = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_BACKGROUND_LOCATION,
            {
              title: "App Permissão de Localização em Segundo Plano",
              message: "Permita para continuar",
              buttonNeutral: "Pergunte-me depois",
              buttonNegative: "Cancelar",
              buttonPositive: "OK"
            }
          );
          const grantedFine = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            {
              title: "App Permissão de Localização",
              message: "Permita para continuar",
              buttonNeutral: "Pergunte-me depois",
              buttonNegative: "Cancelar",
              buttonPositive: "OK"
            }
          );
          console.log('results',PermissionsAndroid.RESULTS.GRANTED)
          console.log('grantedFine', grantedFine)
          if (grantedFine === PermissionsAndroid.RESULTS.GRANTED && grantedBackground === PermissionsAndroid.RESULTS.GRANTED) {
            console.log('Permissão de localização permitida');
          } else {
            alert('Permita a localizaçao em segundo plano para continuar usando o App.');
          }
          
        } catch (err) {
          console.warn(err);
        }
      }
      };
    
    const findCoordinatesFazendeiro = async () => {
      let hasLocationPermission
      if(Platform.OS == "ios") {
        hasLocationPermission = await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE)
      } else { 
        hasLocationPermission = await PermissionsAndroid.check(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );
      }
        if (hasLocationPermission) {
            Geolocation.getCurrentPosition(
            position => {
                setLocation(position.coords);
            },
            error => Alert.alert(error.message),
            {enableHighAccuracy: true, timeout: 20000, maximumAge: 1000},
            );
        } else {
            await requestLocationPermission();
        }
    };

    const findCoordinates = async () => {
      const idAsync = await AsyncStorage.getItem('@Id');
        const hasLocationPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_BACKGROUND_LOCATION
        );
        if (hasLocationPermission && idAsync) {    
          Geolocation.getCurrentPosition(
            position => {
              setLocation(position.coords);
        
              if(!geolocationInterval) {

                saveGeolocation({...position.coords, userId: idAsync})
                //Salva localização a cada minuto
                setGeolocationInterval(
                  setInterval(() => saveGeolocation({...position.coords, userId: idAsync }), 30000)
                )
              }
            },
            error => Alert.alert(error.message),
            {enableHighAccuracy: true, timeout: 20000, maximumAge: 1000},
          );
        } else {
          await requestLocationPermission();
        }
      };


    return (
        <PermissionContext.Provider value={{findCoordinates, requestLocationPermission, findCoordinatesFazendeiro}}>
            {children}
        </PermissionContext.Provider>
    );
}
