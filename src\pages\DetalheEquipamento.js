import React, { useEffect, useState } from 'react'
import {
  StyleSheet, View,
  Text,
  TouchableOpacity,
  Dimensions,
  Button,
  Touchable,
  ActivityIndicator
} from "react-native";
import { Icon } from 'react-native-elements';
import axios from 'axios'
import { URL_API } from '../services/urls';
import { useNavigation, useRoute } from '@react-navigation/core';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

function DetalheEquipamento() {

  const [loading, setLoading] = useState(true)
  const navigation = useNavigation()
  const [arm, setArm] = useState(false)
  const [detalhesEquipamento, setDetalhesEquipamento] = useState()
  const [disableAcao, setDisableAcao] = useState(false)

  const route = useRoute();

  const equipamento_id = route.params.equipamento_id

  const armar = () => {
    setDisableAcao(true)
    axios.post(URL_API + "armar_equipamento", {
      equipamento_id: equipamento_id
    })
      .then(res => {
        setTimeout(function () {
          detalhes()
          setDisableAcao(false)
        }, 1500)
      })
      .catch(err => {
        console.log(err)
      })
  }

  const desarmar = () => {

    setDisableAcao(true)
    axios.post(URL_API + "desarmar_equipamento", {
      equipamento_id: equipamento_id
    })
      .then(res => {
        
        setTimeout(function () {
          detalhes()
          setDisableAcao(false)
        }, 1500)
        
      })
      .catch(err => {
        console.log(err)
      })
  }

  const detalhes = () => {
    axios.post(URL_API + 'detalhes_por_equipamento_id', {
      equipamento_id: equipamento_id
    })
      .then(res => {
        setLoading(false)
        console.log(res.data.data)
        setDetalhesEquipamento(res.data.data)
      })
      .catch(err => {
        console.log(err)
      })
  }

  const disparar = (ms) => {
    setDisableAcao(true)
    axios.post(URL_API + "disparar_x_segundos", {
      equipamento_id: equipamento_id,
      ms_disparo: ms
    })
    .then(res => {
      setTimeout(function () {
        detalhes()
        setDisableAcao(false)
      }, ms + 2000)
    })
  }

  useEffect(() => {
    detalhes()
  }, [])

  return (
    <View style={styles.main}>
      <View style={styles.row}>
        {!loading ?
          <>
            {
              detalhesEquipamento && detalhesEquipamento.status.connection == 1 ?
                <TouchableOpacity style={styles.conectadoButton}>
                  <Icon
                    name='wifi'
                    type="material"
                    color='#3F98D0'
                    style={styles.icon}
                    size={45}
                  />
                  <Text style={styles.textButton}>Conectado</Text></TouchableOpacity> :
                <View style={styles.naoConectadoButton}>
                  <Icon
                    name='wifi'
                    type="material"
                    color='#FFF'
                    style={styles.icon}
                    size={45}
                  />
                  <Text style={styles.naoConectadoTexto}>Não Conectado</Text>
                </View>
            }

            {detalhesEquipamento && detalhesEquipamento.status.armed == 1 ?
              <TouchableOpacity
                onPress={desarmar}
                style={styles.conectadoButton}
                disabled={disableAcao}
              >
                <Icon
                  name="lock"
                  type="font-awesome"
                  color="#3F98D0"
                  style={styles.icon}
                  size={45} />
                <Text style={styles.textButton}>Pronto</Text>
              </TouchableOpacity> :
              <TouchableOpacity
                onPress={armar}
                style={styles.naoProntoButton}
                disabled={disableAcao}
              >
                <Icon
                  name="unlock"
                  type="font-awesome"
                  color="#FFF"
                  style={styles.icon}
                  size={45} />
                <Text style={styles.naoConectadoTexto}>Não está pronto</Text>
              </TouchableOpacity>
            }

            <View style={styles.conectadoButton}>
              <Icon
                name="opacity"
                type="material"
                color="#3F98D0"
                style={styles.icon}
                size={45} />
              <Text style={styles.textButton}>Fluído: {detalhesEquipamento && detalhesEquipamento.status.fluid_level}%</Text>
            </View>

            <View style={styles.conectadoButton}>
              <Icon
                name="thermometer"
                type="font-awesome"
                color="#3F98D0"
                style={styles.icon}
                size={45} />
              <Text style={styles.textButton}>Temperatura: {detalhesEquipamento && detalhesEquipamento.status.boil_temp}º</Text>
            </View>

            <TouchableOpacity 
              style={styles.paddingTop}
              disabled={(detalhesEquipamento && detalhesEquipamento.status.armed != 1) || disableAcao}
              onPress={() => disparar(1000)}
              >
              <View>
                <Text style={styles.textButton}>Disparar 1 segundo</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity 
            style={styles.paddingTop10}
            disabled={(detalhesEquipamento && detalhesEquipamento.status.armed != 1) || disableAcao}
            onPress={() => disparar(2000)}
            >
              <View>
                <Text style={styles.textButton}>Disparar 2 segundos</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity style={styles.paddingTop10}
            disabled={(detalhesEquipamento && detalhesEquipamento.status.armed != 1) || disableAcao}
            onPress={() => disparar(5000)}
            >
              <View>
                <Text style={styles.textButton}>Disparar 5 segundos</Text>
              </View>
            </TouchableOpacity>
          </>
          :
          <View style={styles.horizontal}>
            <ActivityIndicator size="large" />
          </View>}
      </View>
    </View >
  )
}

const styles = StyleSheet.create({
  main: {
    flex: 1,
    alignItems: 'center',
  },
  dispararButton: {
    padding: 15,
    borderRadius: 30,
    marginTop: '2%',
    width: '100%',
    backgroundColor: '#fff',
    elevation: 1
  },
  dispararButtonDisabled: {
    padding: 15,
    borderRadius: 30,
    marginTop: '2%',
    width: '100%',
    backgroundColor: 'grey',
    elevation: 1
  },
  row: {
    justifyContent: 'space-between',
    paddingHorizontal: 22,
    flexWrap: 'wrap',
    flex: 1,
    flexDirection: 'row',
  },
  conectadoButton: {
    padding: 15,
    borderRadius: 30,
    marginTop: '2%',
    width: '47%',
    backgroundColor: '#fff',
    elevation: 1
  },

  paddingTop: {
    padding: 15,
    borderRadius: 30,
    marginTop: '30%',
    width: '100%',
    backgroundColor: '#fff',
    elevation: 1
  },
  paddingTop10: {
    padding: 15,
    borderRadius: 30,
    marginTop: '10%',
    width: '100%',
    backgroundColor: '#fff',
    elevation: 1
  },
  naoConectadoButton: {
    padding: 15,
    borderRadius: 30,
    marginTop: '2%',
    width: '47%',
    backgroundColor: '#b53737',
    elevation: 1,
  },
  naoConectadoTexto: {
    color: '#fff',
    fontSize: 15,
    textAlign: 'center'
  },
  naoProntoButton: {
    padding: 15,
    borderRadius: 30,
    marginTop: '2%',
    width: '47%',
    backgroundColor: '#F27F0C',
    elevation: 1,
  },
  textButton: {
    color: '#000',
    fontSize: 15,
    textAlign: 'center',
  },
  icon: {
    alignItems: 'center'
  },
  panicButtonDisabled: {
    padding: 15,
    borderRadius: 30,
    marginTop: '-15%',
    width: '47%',
    backgroundColor: 'gray',
    elevation: 1,
    // marginHorizontal: '2%'
  },
  textButtonDisabled: {
    color: '#fff',
    fontSize: 15,
    textAlign: 'left',
  },
  container: {
    flex: 1,
    justifyContent: "center"
  },
  horizontal: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center'
  }
});

export default DetalheEquipamento