import 'react-native';
import InformacaoGetMotivosInformacao from '../src/containers/Fazendeiro/InformacaoGetMotivosInformacao';


it('Teste InformacaoGetMotivosInformacao Sucesso', async () => {
  
  const axios = {
    get: async () => {
      return 200
    }
  }
  const retorno = await InformacaoGetMotivosInformacao(axios)
  expect(200).toBe(200);

});

it('Teste InformacaoGetMotivosInformacao error ', async () => {
  
  const axios = {
    get: async () => {
      return 400
    }
  }
  const retorno = await InformacaoGetMotivosInformacao(axios)
  expect(400).toBe(400);
});