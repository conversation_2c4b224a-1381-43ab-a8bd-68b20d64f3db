import React, { useState, useContext, useEffect } from 'react';
import {useSocketContext} from './socketContext';
import { useImmer } from "use-immer";
import axios from 'axios';
import { URL_SOCKET } from '../services/urls';

const AlertContext = React.createContext();

export function useAlertContext() {
  return useContext(AlertContext);
}  

export function AlertProvider({ children }) {

    const [panico, setPanico] = useState(false);
    const {socket} = useSocketContext();
    const [datas, setData] = useImmer([]);

    const getPanicos = () => {
        axios.get(URL_SOCKET+'todos_panicos')
        .then(data => {
            console.log('dados acionados',data.data);
            const dados = data.data.filter(data=> data.STATUS == 0 || data.STATUS == 1)
            setData(dados)
            
            /* for(let i=0; i < data.data.length; i++){
                if(data.data[i].IDENTIFICACAO != "FD"){
                    setData(draft => {draft.push([data.data[i].NOME,'',data.data[i].ID,'acionado',data.data[i].LATITUDE,data.data[i].LONGITUDE])}
                    );
                }
                else{
                    setData(draft => {draft.push([data.data[i].NOME,data.data[i].ENDERECO,data.data[i].ID,'acionado',data.data[i].LATITUDE,data.data[i].LONGITUDE])}
                    );
                }
            } */
        }).catch(err => {
            console.error('erro ocorrencias',JSON.stringify(err)); 
        })
    }

    useEffect(() => {
        ////////////////////////////////PEGAR O ALERTAS ACIONADOS/////////////////////////////////////////////////
        ///////////////////////////////////////ALERTAS EM ANDAMENTO//////////////////////////////////////////////
        // axios.get(URL_SOCKET+'panico_ocorrencias_andamento')
        // .then(data => {
        //     console.log('dados em andamento',data.data);
            
        //     for(let i=0; i < data.data.length; i++){
        //         if(data.data[i].IDENTIFICACAO != "FD"){
        //             setData(draft => {draft.push([data.data[i].NOME,'',data.data[i].ID,'andamento',data.data[i].LATITUDE,data.data[i].LONGITUDE])}
        //             );
        //         }
        //         else{
        //             setData(draft => {draft.push([data.data[i].NOME,data.data[i].ENDERECO,data.data[i].ID,'andamento',data.data[i].LATITUDE,data.data[i].LONGITUDE])}
        //             );
        //         }
        //     }
        // }).catch(err => {
        //     console.error('erro andamento',JSON.stringify(err)); 
        // })
          
        socket.on('Notify',(data)=>{
            // console.log('panicooooo',data);
            // setPanico(true);
            // if(data.dados_quem_foi[0]['IDENTIFICACAO']){
            //     setData(draft => {draft.push([data.dados_quem_foi[0]['NOME'],'',data.last_id,'acionado', data.dados_panico.latitude, data.dados_panico.longitude])}
            //     );
            // }
            // else{
            //     setData(draft => {draft.push([data.dados_quem_foi[0][2],data.dados_quem_foi[0][6],data.last_id[0],'acionado', data.dados_panico.latitude, data.dados_panico.longitude])}
            //     );
            // }
            setPanico(true)
            getPanicos();
        });

        socket.on('Atenção ocorrencia cancelada', (res)=>{
            getPanicos();
        })

        socket.on('Atenção ocorrencia finalizada', data => {
            getPanicos();
        })

         return () => {
            socket.off('panico');
           };
    }, [socket]);



  return (
    <AlertContext.Provider value={{datas}}>
      {children}
    </AlertContext.Provider>
  );
}