import React, { useState, useContext, useRef, useEffect } from 'react';
import { Alert, AppState, StyleSheet, TouchableOpacity } from "react-native";
import {
  View,
  Text,
  StatusBar,
  ScrollView,
  TextInput,
  VirtualizedList,
  FlatList,
  Linking
} from 'react-native';

import { useNavigation, useRoute } from '@react-navigation/native';

import { Icon } from 'react-native-elements';

import { Dimensions } from 'react-native'
import axios from 'axios';
import { URL_API } from '../../services/urls';


const window = Dimensions.get('window');
const width = window.width;
const height = window.height;



function VisitasFazenda(props) {

  const navigation = useNavigation();
  const route = useRoute();

  const id = route.params.id

  const [visitas, setVisitas] = useState()
  const [loading, setLoading] = useState(false)

  useEffect(()=>{
      setLoading(true)
      axios.post(URL_API+'detalhe_visita',{
          id: id
      })
      .then(res=>{
        const visita = res.data
        const tempVisita = [...visita]
        visita.map((data)=>{
          const date = data.data_inicio.split('-')
          data.data_inicio = date[2]+'-'+date[1]+'-'+date[0]
        })
        setVisitas(tempVisita)
        setLoading(false)
      })
      .catch(res=>{
          Alert.alert('Aviso','Essa fazenda não possui visitas!')
      })
  },[])
  console.log('kjsbakjd',visitas)
 const Item = ({ item }) => (
   <>
      
    <View style={styles.card}>
        <View style={{marginTop:'2%', marginLeft:'2%', flex:1}}>
            <Text style={styles.titulo}>Visita #{item.id}</Text>
            <View style={styles.row}>
                <Text style={styles.tipo}>Data da Visita: </Text>
                <Text style={styles.dado}>{item.data_inicio}</Text>
            </View>
            <View style={styles.row}>
                <Text style={styles.tipo}>Viatura: </Text>
                <Text style={styles.dado}>#{item.id_viat}</Text>
            </View>
            <View style={styles.row}>
                <Text style={styles.tipo}>Responsável Visita: </Text>
                <Text style={styles.dado}>{item.nome}</Text>
            </View>
            <Text style={styles.status}>{item.status}</Text>
        </View>
    </View>

  </>
 
                     
   );
    
  
  return (
        <>
            <View style={styles.blueCard}>
                
            </View>
            <View style={styles.main}>
                <Text style={styles.Text}>Confira a lista de visitas na propriedade</Text>
                <FlatList
                    data={visitas}
                    renderItem={Item}
                    keyExtractor={item => item.id}
                />
            </View>
        </>
  );
}
const styles = StyleSheet.create({
    main:{
        flex:1,
        backgroundColor: '#fff',
        borderTopLeftRadius:40,
        borderTopRightRadius:40,
        marginTop:'-8%'
    },
    blueCard:{
        backgroundColor:'rgba(0, 118, 193, 1)',
        height:'6%'
    },
    card:{
        flex:1,
        height: 220,
        width:'95%',
        backgroundColor:'#fff',
        padding: 5,
        elevation:2,
        borderRadius:30,
        padding: 10,
        marginTop: '2.5%',
        marginBottom: '2.5%',
        alignSelf:'center'
    },
    row:{
        // flex:1,
        flexDirection: 'row',
        marginVertical: '2%'
    },
    Text:{
        marginTop:'5%',
        textAlign:'center',
        marginBottom:'2%',
        color:'#6D6D6D'
    },
    titulo:{
        fontSize:20,
        fontWeight:'bold'
    },
    tipo:{
        fontSize:16
    },
    dado:{
        fontSize:16,
        color: '#6D6D6D'
    },
    status:{
        fontSize: 16,
        fontWeight:"bold",
        color: 'rgba(0, 118, 193, 1)',
        marginTop:'5%'
    }
  });
export default VisitasFazenda;