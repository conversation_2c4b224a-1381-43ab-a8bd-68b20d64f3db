import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, Text, Alert, ActivityIndicator } from 'react-native'
import { useUserContext } from '../context/AuthContext'
import { useLocation } from '../context/LocationContext'
import { saveGeolocation } from '../api'
import axios from 'axios';
import { useSocketContext } from '../context/socketContext';
import { Dimensions, Linking } from 'react-native';
import AsyncStorage from '@react-native-community/async-storage';
import { URL_SOCKET, URL_API } from '../services/urls'
import { NavigationContainer } from '@react-navigation/native';
import { useNavigation } from '@react-navigation/core';
const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

function BotaoTracarRota({ data,sett,status }) {
  const [id_viatura_atual, setId_viatura_atual] = useState(0);
const navigation = useNavigation();
const pressed=()=>{
  aCaminho()  
}

const aCaminho = () => {
  console.log(data.ID)
  axios.post(URL_API + "panico_acaminho", {
    id: data.ID,
    id_viatura: id_viatura_atual
  })
  .then(res => {
    checkState()
  })
  .catch(err => {
    console.log(err)
  })
}

const checkState = () => {
  if(!status){
    Linking.openURL('https://www.google.com/maps/dir/?api=1&destination=' + data.LATITUDE + ',' + data.LONGITUDE + '')
    sett[1]([...sett[0],data.ID])
  }
  else{
    console.log("object")
    navigation.navigate('Qr2',{id: data.ID})
  }
}

const getViatura = async () => {
  const viatura = await AsyncStorage.getItem('@Viatura');

  setId_viatura_atual(viatura)
}

getViatura()

  return (
    <View style={styles.main}>
      <TouchableOpacity
        // onPress={()=>console.log(data.ID)} 
        onPress={() => pressed()}
        style={styles.panicButton}>
        <Text style={styles.textButton}>{status?"Validar visita":"Traçar rota"}</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  main: {
    flex: 1,
    alignItems: 'center'
  },
  panicButton: {
    padding: 15,
    backgroundColor: '#b94343',
    borderRadius: 30,
    marginTop: 15,
    width: width - 70,
  },
  panicButtonDisabled: {
    padding: 15,
    backgroundColor: "gray",
    borderRadius: 30,
    marginTop: 15,
    width: width - 50,
  },
  textButton: {
    color: 'white',
    fontSize: 13,
    textAlign: 'center',
  }
});

export default BotaoTracarRota;