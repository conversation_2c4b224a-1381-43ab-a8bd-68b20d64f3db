PODS:
  - boost-for-react-native (1.63.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.64.2)
  - FBReactNativeSpec (0.64.2):
    - RCT-Folly (= 2020.01.13.00)
    - RCTRequired (= 0.64.2)
    - RCTTypeSafety (= 0.64.2)
    - React-Core (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - glog (0.3.5)
  - Mapbox-iOS-SDK (5.9.0):
    - MapboxMobileEvents (= 0.10.2)
  - MapboxMobileEvents (0.10.2)
  - Permission-LocationAlways (3.1.0):
    - RNPermissions
  - Permission-LocationWhenInUse (3.1.0):
    - RNPermissions
  - RCT-Folly (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
    - RCT-Folly/Default (= 2020.01.13.00)
  - RCT-Folly/Default (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
  - RCTRequired (0.64.2)
  - RCTTypeSafety (0.64.2):
    - FBLazyVector (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTRequired (= 0.64.2)
    - React-Core (= 0.64.2)
  - React (0.64.2):
    - React-Core (= 0.64.2)
    - React-Core/DevSupport (= 0.64.2)
    - React-Core/RCTWebSocket (= 0.64.2)
    - React-RCTActionSheet (= 0.64.2)
    - React-RCTAnimation (= 0.64.2)
    - React-RCTBlob (= 0.64.2)
    - React-RCTImage (= 0.64.2)
    - React-RCTLinking (= 0.64.2)
    - React-RCTNetwork (= 0.64.2)
    - React-RCTSettings (= 0.64.2)
    - React-RCTText (= 0.64.2)
    - React-RCTVibration (= 0.64.2)
  - React-callinvoker (0.64.2)
  - React-Core (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/CoreModulesHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/Default (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/DevSupport (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.2)
    - React-Core/RCTWebSocket (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-jsinspector (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTBlobHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTImageHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTTextHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTWebSocket (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-CoreModules (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/CoreModulesHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-RCTImage (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-cxxreact (0.64.2):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsinspector (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - React-runtimeexecutor (= 0.64.2)
  - React-jsi (0.64.2):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-jsi/Default (= 0.64.2)
  - React-jsi/Default (0.64.2):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
  - React-jsiexecutor (0.64.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-perflogger (= 0.64.2)
  - React-jsinspector (0.64.2)
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-date-picker (4.1.0):
    - React-Core
  - react-native-geolocation-service (5.3.0-beta.3):
    - React
  - react-native-image-picker (4.1.2):
    - React-Core
  - react-native-mapbox-gl (8.3.0):
    - Mapbox-iOS-SDK (~> 5.9.0)
    - React
    - React-Core
    - react-native-mapbox-gl/DynamicLibrary (= 8.3.0)
    - react-native-mapbox-gl/StaticLibraryFixer (= 8.3.0)
  - react-native-mapbox-gl/DynamicLibrary (8.3.0):
    - Mapbox-iOS-SDK (~> 5.9.0)
    - React
    - React-Core
  - react-native-mapbox-gl/StaticLibraryFixer (8.3.0):
    - Mapbox-iOS-SDK (~> 5.9.0)
    - React
    - React-Core
  - react-native-pager-view (5.4.7):
    - React-Core
  - react-native-safe-area-context (3.3.2):
    - React-Core
  - react-native-viewpager (5.0.11):
    - React-Core
  - React-perflogger (0.64.2)
  - React-RCTActionSheet (0.64.2):
    - React-Core/RCTActionSheetHeaders (= 0.64.2)
  - React-RCTAnimation (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTAnimationHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTBlob (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTBlobHeaders (= 0.64.2)
    - React-Core/RCTWebSocket (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-RCTNetwork (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTImage (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTImageHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-RCTNetwork (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTLinking (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - React-Core/RCTLinkingHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTNetwork (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTNetworkHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTSettings (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTSettingsHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTText (0.64.2):
    - React-Core/RCTTextHeaders (= 0.64.2)
  - React-RCTVibration (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTVibrationHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-runtimeexecutor (0.64.2):
    - React-jsi (= 0.64.2)
  - ReactCommon/turbomodule/core (0.64.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.2)
    - React-Core (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-perflogger (= 0.64.2)
  - RNCAsyncStorage (1.12.1):
    - React-Core
  - RNCPicker (1.8.1):
    - React-Core
  - RNCPushNotificationIOS (1.10.1):
    - React-Core
  - RNDateTimePicker (3.5.2):
    - React-Core
  - RNPermissions (3.1.0):
    - React-Core
  - RNScreens (3.8.0):
    - React-Core
    - React-RCTImage
  - RNSVG (12.1.1):
    - React
  - RNVectorIcons (9.0.0):
    - React-Core
  - Yoga (1.14.0)

DEPENDENCIES:
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - Permission-LocationAlways (from `../node_modules/react-native-permissions/ios/LocationAlways`)
  - Permission-LocationWhenInUse (from `../node_modules/react-native-permissions/ios/LocationWhenInUse`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - react-native-camera (from `../node_modules/react-native-camera`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - "react-native-mapbox-gl (from `../node_modules/@react-native-mapbox-gl/maps`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-viewpager (from `../node_modules/@react-native-community/viewpager`)"
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-community/async-storage`)"
  - "RNCPicker (from `../node_modules/@react-native-community/picker`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - boost-for-react-native
    - Mapbox-iOS-SDK
    - MapboxMobileEvents

EXTERNAL SOURCES:
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  Permission-LocationAlways:
    :path: "../node_modules/react-native-permissions/ios/LocationAlways"
  Permission-LocationWhenInUse:
    :path: "../node_modules/react-native-permissions/ios/LocationWhenInUse"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-mapbox-gl:
    :path: "../node_modules/@react-native-mapbox-gl/maps"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-viewpager:
    :path: "../node_modules/@react-native-community/viewpager"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-community/async-storage"
  RNCPicker:
    :path: "../node_modules/@react-native-community/picker"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost-for-react-native: 39c7adb57c4e60d6c5479dd8623128eb5b3f0f2c
  DoubleConversion: cf9b38bf0b2d048436d9a82ad2abe1404f11e7de
  FBLazyVector: e686045572151edef46010a6f819ade377dfeb4b
  FBReactNativeSpec: f8706a994a74c26d0352d3bc36105189d2555272
  glog: 73c2498ac6884b13ede40eda8228cb1eee9d9d62
  Mapbox-iOS-SDK: a5915700ec84bc1a7f8b3e746d474789e35b7956
  MapboxMobileEvents: 2bc0ca2eedb627b73cf403258dce2b2fa98074a6
  Permission-LocationAlways: f1e021c3b348946cd7c5760172925b749e5d07a6
  Permission-LocationWhenInUse: d98db702ec75e93a3ff94bc297d0b66ea04231e1
  RCT-Folly: ec7a233ccc97cc556cf7237f0db1ff65b986f27c
  RCTRequired: 6d3e854f0e7260a648badd0d44fc364bc9da9728
  RCTTypeSafety: c1f31d19349c6b53085766359caac425926fafaa
  React: bda6b6d7ae912de97d7a61aa5c160db24aa2ad69
  React-callinvoker: 9840ea7e8e88ed73d438edb725574820b29b5baa
  React-Core: b5e385da7ce5f16a220fc60fd0749eae2c6120f0
  React-CoreModules: 17071a4e2c5239b01585f4aa8070141168ab298f
  React-cxxreact: 9be7b6340ed9f7c53e53deca7779f07cd66525ba
  React-jsi: 67747b9722f6dab2ffe15b011bcf6b3f2c3f1427
  React-jsiexecutor: 80c46bd381fd06e418e0d4f53672dc1d1945c4c3
  React-jsinspector: cc614ec18a9ca96fd275100c16d74d62ee11f0ae
  react-native-camera: 3eae183c1d111103963f3dd913b65d01aef8110f
  react-native-date-picker: 52937fb39322ee5f6c44a10117456b3fa4225d4c
  react-native-geolocation-service: 32e50a2e4e4518b51b829fdfe137562a672d95d2
  react-native-image-picker: 9c8a2687b69300ad9e95cec5d38f35ab9d32467d
  react-native-mapbox-gl: 94ddd813fd5d4fdab9ca9d45ce108125480cfffd
  react-native-pager-view: 741e7606ee2f6e399fc7da777f5519454a4b38c5
  react-native-safe-area-context: 584dc04881deb49474363f3be89e4ca0e854c057
  react-native-viewpager: b99b53127d830885917ef84809c5065edd614a78
  React-perflogger: 25373e382fed75ce768a443822f07098a15ab737
  React-RCTActionSheet: af7796ba49ffe4ca92e7277a5d992d37203f7da5
  React-RCTAnimation: 6a2e76ab50c6f25b428d81b76a5a45351c4d77aa
  React-RCTBlob: 02a2887023e0eed99391b6445b2e23a2a6f9226d
  React-RCTImage: ce5bf8e7438f2286d9b646a05d6ab11f38b0323d
  React-RCTLinking: ccd20742de14e020cb5f99d5c7e0bf0383aefbd9
  React-RCTNetwork: dfb9d089ab0753e5e5f55fc4b1210858f7245647
  React-RCTSettings: b14aef2d83699e48b410fb7c3ba5b66cd3291ae2
  React-RCTText: 41a2e952dd9adc5caf6fb68ed46b275194d5da5f
  React-RCTVibration: 24600e3b1aaa77126989bc58b6747509a1ba14f3
  React-runtimeexecutor: a9904c6d0218fb9f8b19d6dd88607225927668f9
  ReactCommon: 149906e01aa51142707a10665185db879898e966
  RNCAsyncStorage: b03032fdbdb725bea0bd9e5ec5a7272865ae7398
  RNCPicker: 914b557e20b3b8317b084aca9ff4b4edb95f61e4
  RNCPushNotificationIOS: 87b8d16d3ede4532745e05b03c42cff33a36cc45
  RNDateTimePicker: 7658208086d86d09e1627b5c34ba0cf237c60140
  RNPermissions: 4b54095940aea8c03fa3e6c92d4ac3647b31ed4e
  RNScreens: 6e1ea5787989f92b0671049b808aef64fa1ef98c
  RNSVG: 551acb6562324b1d52a4e0758f7ca0ec234e278f
  RNVectorIcons: 4143ba35feebab8fdbe6bc43d1e776b393d47ac8
  Yoga: 575c581c63e0d35c9a83f4b46d01d63abc1100ac

PODFILE CHECKSUM: 2cfae426b408761a008e070953be820e5ece8b41

COCOAPODS: 1.11.2
