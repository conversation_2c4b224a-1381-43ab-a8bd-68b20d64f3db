# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true
org.gradle.jvmargs = -Xmx4608m
# Version of flipper SDK to use with React Native
FLIPPER_VERSION=0.75.1

# Force Gradle to use Java 11
org.gradle.java.home=C:\\Program Files\\Eclipse Adoptium\\jdk-*********-hotspot

MYAPP_RELEASE_STORE_FILE=my-upload-key.keystore
MYAPP_RELEASE_KEY_ALIAS=my-key-alias
MYAPP_RELEASE_STORE_PASSWORD=123456
MYAPP_RELEASE_KEY_PASSWORD=123456

# org.gradle.jvmargs = -Xmx4g

# Mapbox Downloads token - Get this from https://account.mapbox.com/access-tokens/
# You need to create a Downloads:Read token for Maven repository access
MAPBOX_DOWNLOADS_TOKEN=pk.eyJ1Ijoiam9pY3lhbGJ1cXVlcnF1ZSIsImEiOiJja3JueTJ3cjMxeTFzMm5tbHZsc29id2t0In0.eUpkMW39cxUGx3t747gQUA
