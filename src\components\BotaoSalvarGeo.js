import React from 'react';
import { View, Button } from 'react-native'


function BotaoSalvarGeo() {
  const saveGeolocation = () => {
    const URL = "http://ec2-34-214-170-15.us-west-2.compute.amazonaws.com:5001/app_aiba"

    const data = JSON.stringify({"latitude": location.latitude,"longitude": location.longitude});

    return fetch(URL, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: data
    }).then(res => res.json()) 
      .then(res => {
        console.log(res)
      }).catch(err => {
      console.error(err)
    })
  }

  return (
    <View>
      <Button
        title="Salvar geolocalização agora"
        onPress={() => saveGeolocation()}
      />
    </View>
  );
}

export default BotaoSalvarGeo;
