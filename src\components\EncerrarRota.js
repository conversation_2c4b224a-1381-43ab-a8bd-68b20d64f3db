import React, {useState, useEffect} from 'react';
import { View,ScrollView, TouchableOpacity, StyleSheet, Text, TextInput, Alert } from 'react-native'
import { useUserContext } from '../context/AuthContext'
import { useLocation } from '../context/LocationContext'
import { saveGeolocation } from '../api'
import axios from 'axios'; 
import { useSocketContext } from '../context/socketContext';
import { Dimensions } from 'react-native';
import AsyncStorage from '@react-native-community/async-storage';
import {URL_API, URL_SOCKET} from '../services/urls'
import QRCodeScanner from 'react-native-qrcode-scanner';
import { Icon } from 'react-native-elements';
import { useNavigation } from '@react-navigation/native';
import { useRoute } from '@react-navigation/native';
import { Picker } from '@react-native-picker/picker';
import { Plataform } from 'react-native'


const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

function EncerrarRota() {

  const navigation = useNavigation();

  const route = useRoute();

  const { id, justificativa } = route.params;
  //console.log("justificativa do modal:", justificativa)

  const [km, setKm] = useState();
  const [peopleApproached, setPeopleApproached] = useState();
  const [fourWheelVehicle, setFourWheelVehicle] = useState();
  const [twoWheelVehicle, setTwoWheelVehicle] = useState();
  const [seizedWeapons,setSeizedWeapons] = useState();
  const [policeStation, setPoliceStation] = useState(0);
  const [establishmentsCovered, setEstablishmentsCovered] = useState();
  const [loadRecovered, setLoadRecovered] = useState();
  const [repairedVehicle, setRepairedVehicle] = useState();

  const clearIdRota = async() => {
    await AsyncStorage.removeItem('@IdRota');
    await AsyncStorage.removeItem('@Viatura');
    navigation.reset({
      index: 0,
      routes: [{ name: 'IniciarServico' }],
    });
  }

  const handleValidar = () => {
    if(km  === '' || km === undefined){
      Alert.alert('Preencha a kilometragem final da viatura.')
      return
    }
    if(peopleApproached  === '' || peopleApproached === undefined){
      Alert.alert('Preencha a quantidade de pessoas abordadas.')
      return
    }
    if(fourWheelVehicle  === '' || fourWheelVehicle === undefined){
      Alert.alert('Preencha a quantidade de veículos de 4 rodas.')
      return
    }
    if(twoWheelVehicle  === '' || twoWheelVehicle === undefined){
      Alert.alert('Preencha a quantidade de veículos de 2 rodas.')
      return
    }
    if(seizedWeapons  === '' || seizedWeapons === undefined){
      Alert.alert('Preencha a quantidade de armas apreendidas.')
      return
    }
    if(establishmentsCovered  === '' || establishmentsCovered === undefined){
      Alert.alert('Preencha a quantidade de estabelecimentos abordados.')
      return
    }
    if(loadRecovered  === '' || loadRecovered === undefined){
      Alert.alert('Preencha a quantidade de carga recuperada.')
      return
    }
    if(repairedVehicle  === '' || repairedVehicle === undefined){
      Alert.alert('Preencha a quantidade de veiculos recuperados .')
      return
    }
    axios.put(`${URL_API}encerrar_rota`,{
      id:id.toString(),
      km_final_viatura:km.toString(),
      justificativa:justificativa,
      pessoas_abordadas:peopleApproached.toString(),
      veiculos_4_rodas:fourWheelVehicle.toString(),
      veiculos_2_rodas:twoWheelVehicle.toString(),
      armas_apreendidas:seizedWeapons.toString(),
      apresentacao_na_delegacia:policeStation.toString(),
      estabelecimento_abordado:establishmentsCovered.toString(),
      carga_recuperada:loadRecovered.toString(),
      veiculo_recuperado:repairedVehicle.toString()
    })
    .then(res =>{
      Alert.alert('', 'Rota encerrada com sucesso!');
      clearIdRota();
    })
    .catch(message =>{
      console.log('erro encerrar rota',message.message)
      Alert.alert('', 'Ocorreu um problema, tente novamente. Os campos devem ser preenchidos com valores numéricos.')   
    })
  }
  
  return (
    <View style={styles.main}>
      <ScrollView  contentInsetAdjustmentBehavior="automatic" style={styles.forms}>
        <Text style={styles.label}>Pessoas abordadas</Text>
        <TextInput
          style={styles.input}
          onChangeText={(e)=> setPeopleApproached(e)}
          value={peopleApproached}
          placeholder="Digite aqui"
          placeholderTextColor='gray'
          maxLength={40}
        />
        <Text style={styles.label}>Veículos 4 rodas abordados</Text>
        <TextInput
          style={styles.input}
          onChangeText={(e)=> setFourWheelVehicle(e)}
          value={fourWheelVehicle}
          placeholder="Digite aqui"
          placeholderTextColor='gray'
          maxLength={40}
        />
        <Text style={styles.label}>Veículos 2 rodas abordados</Text>
        <TextInput
          style={styles.input}
          onChangeText={(e)=> setTwoWheelVehicle(e)}
          value={twoWheelVehicle}
          placeholder="Digite aqui"
          placeholderTextColor='gray'
          maxLength={40}
        />
        <Text style={styles.label}>Armas apreendidas</Text>
        <TextInput
          style={styles.input}
          onChangeText={(e)=> setSeizedWeapons(e)}
          value={seizedWeapons}
          placeholder="Digite aqui"
          placeholderTextColor='gray'
          maxLength={40}
        />
        <Text style={styles.label}>Estabelecimentos abordados</Text>
        <TextInput
          style={styles.input}
          onChangeText={(e)=> setEstablishmentsCovered(e)}
          value={establishmentsCovered}
          placeholder="Digite aqui"
          placeholderTextColor='gray'
          maxLength={40}
        />
        <Text style={styles.label}>Carga recuperada</Text>
        <TextInput
          style={styles.input}
          onChangeText={(e)=> setLoadRecovered(e)}
          value={loadRecovered}
          placeholder="Digite aqui"
          placeholderTextColor='gray'
          maxLength={40}
        />
        <Text style={styles.label}>Veículo recuperado</Text>
        <TextInput
          style={styles.input}
          onChangeText={(e)=> setRepairedVehicle(e)}
          value={repairedVehicle}
          placeholder="Digite aqui"
          placeholderTextColor='gray'
          maxLength={40}
        />
        {/* <Text style={styles.label}>Apresentação na delegacia?</Text>
        <View style={styles.picker}>
          <Picker
            selectedValue={policeStation}
            style={styles.inputPicker}
            itemStyle={{ height: '100%', width: '100%' }}
            onValueChange={(itemValue, itemIndex) =>
                setPoliceStation(itemValue)
            }>
            <Picker.Item label='Sim' value={0} />
            <Picker.Item label='Não' value={1} />
          </Picker>
        </View> */}
        <Text style={styles.label}>Kilometragem final da viatura</Text>
        <TextInput
          style={styles.inputBotton}
          onChangeText={(e)=> setKm(e)}
          value={km}
          placeholder="Digite aqui"
          placeholderTextColor='gray'
          maxLength={40}
        />
      </ScrollView>
      <TouchableOpacity style={styles.buttonValidar} onPress={() => handleValidar()}>
        <Text style={styles.textButton}>Encerrar Rota</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  main:{
    flex: 1,
  },
  title:{
    marginTop:'-15%',
    width:'80%',
    alignItems:"center"
  },
  titleExtras:{
    marginTop:"10%",
    alignSelf:"center"
  },
  titleExtrasText:{
    fontSize:18
  },
  extras:{
    flex:1,
    marginTop: '50%',
    flexDirection:"row",
    justifyContent:'space-around',
  },
  touchable:{
    alignItems:"center"
  },
  touchableText:{
    fontSize:17,
    fontWeight:'bold'
  },
  buttonValidar:{
    backgroundColor:'rgba(0, 118, 193, 1)',
    alignItems:'center',
    width:'100%',
    alignSelf: 'center',
    padding: '4%',
    position:'relative',
  },
  textButton:{
      color:'#fff',
      fontSize:17
  },
  forms:{
    marginLeft:'6%',
    flex:1,
  },
  label:{
    fontSize: 18,
    marginBottom: '5%',
    marginTop: '5%',
    color:'#5D5D5D'
  },
  input: {
    width: '92%',
    borderWidth: 1,
    padding: 10,
    borderColor:'#fff',
    borderRadius: 30,
    elevation:2,
    backgroundColor: '#fff',
    color: '#000',
  },
  inputBotton: {
    width: '92%',
    borderWidth: 1,
    padding: 10,
    borderColor:'#fff',
    borderRadius: 30,
    elevation:2,
    backgroundColor: '#fff',
    color: '#000',
    marginBottom:'5%',
  },
  picker: {
    borderRadius: 30,
    borderColor: '#fff',
    overflow: 'hidden',
    width: '92%',
    backgroundColor: '#fff',
    elevation: 1
}
});

export default EncerrarRota;