import 'react-native';
import InformacoesHandleEnviar from '../src/containers/Fazendeiro/InformacoesHandleEnviar';


it('Teste InformacoesHandleEnviar Sucesso', async () => {
  const navigation = {
    goBack: () => {}
  }

  const axios = {
    post: async () => {
      return 200
    }
  }

  const idProprietario = 1;
  const descricao = 'Roubo a mão armada'
  const delegacia = 0;
  const id_fazenda = 1;

  const retorno = await InformacoesHandleEnviar(axios, navigation,descricao, idProprietario,id_fazenda,delegacia)
  expect(200).toBe(200);

});

//<PERSON><PERSON> passed
it('Teste InformacoesHandleEnviar Error', async () => {
  const navigation = {
    goBack: () => {}
  }

  const axios = {
    post: async () => {
      return 400
    }
  }

  const idProprietario = 1;
  const descricao = ''
  const delegacia = 0;
  const id_fazenda = 1;

  const retorno = await InformacoesHandleEnviar(axios, navigation,descricao, idProprietario,id_fazenda,delegacia)
  expect(retorno).toBe(undefined);
  
});