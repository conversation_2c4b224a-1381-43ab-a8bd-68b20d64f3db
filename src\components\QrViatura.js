import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, Text, Alert, Modal, TextInput } from 'react-native'
import axios from 'axios';
import { Dimensions } from 'react-native';
import { URL_API, URL_SOCKET } from '../services/urls'
import QRCodeScanner from 'react-native-qrcode-scanner';
import { useNavigation, useRoute } from '@react-navigation/core';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

function QrViatura() {
    const navigation = useNavigation();
    const route = useRoute()
    const [idFazenda, setIdFazenda] = useState()
    const [modalVisible, setModalVisible] = useState(false);
    const [resumo, setResumo] = useState()

    const id = route.params.id

    const handleEnviarQrCode = (e) => {
        setIdFazenda(Number(e.data))
        setModalVisible(true)
    }

    const handleCancelarPanicoPress = () =>{
        axios
      .post(URL_SOCKET + 'panico_finalizado', {
        id: id,
        resumo: resumo
      })
      .then(res => {
        Alert.alert('',`Chamado Encerrado!`);
        setModalVisible(!modalVisible)
        navigation.navigate('Policial')
      })
      .catch(res =>{
        Alert.alert('',res);
      })
    }

    return (
        <>
        <View style={styles.main}>

            <QRCodeScanner
                onRead={e => { handleEnviarQrCode(e) }}
                showMarker={true}
                checkAndroid6Permissions={true}
                // topContent={
                //     // <TouchableOpacity onPress={() => { navigation.navigate("nop") }} style={{ height: 50, width: 100 }}>
                //     //     <Text>Hello</Text>
                //     // </TouchableOpacity>
                // }
            />
        </View>
        <Modal
          animationType="fade"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => {
            Alert.alert("Modal has been closed.");
            setModalVisible(!modalVisible);
          }}
        >
          <View style={styles.centeredView}>
            <View style={styles.modalView}>
              <Text style={styles.modalText}>Resumo da ocorrência</Text>    
              <TextInput
                    style={styles.input}
                    onChangeText={(e)=> setResumo(e)}
                    value={resumo}
                    placeholder="Digite aqui"
                    placeholderTextColor='gray'
                    maxLength={40}
                    numberOfLines={6}
                    multiline
                />
                <View style={{flexDirection:'row', marginTop:'5%', justifyContent:'space-between'}}>
                  <TouchableOpacity
                    style={styles.voltar}
                    onPress={() => setModalVisible(!modalVisible)}
                  >
                    <Text style={styles.textoBotao}>Voltar</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.cancelar}
                    onPress={() => handleCancelarPanicoPress()}
                  >
                    <Text style={styles.textoBotao}>Encerrar pânico</Text>
                  </TouchableOpacity>
                </View>
            </View>
          </View>
        </Modal>
        </>
    )
}

const styles = StyleSheet.create({
    main: {
        flex: 1,
    },
    title: {
        marginTop: '-15%',
        width: '80%',
        alignItems: "center"
    },
    titleExtras: {
        marginTop: "10%",
        alignSelf: "center"
    },
    titleExtrasText: {
        fontSize: 18
    },
    extras: {
        flex: 1,
        marginTop: '50%',
        flexDirection: "row",
        justifyContent: 'space-around',
        // marginHorizontal:'10%'
    },
    touchable: {
        alignItems: "center"
    },
    touchableText: {
        fontSize: 17,
        fontWeight: 'bold'
    },
    buttonValidar: {
        backgroundColor: 'rgba(0, 118, 193, 1)',
        alignItems: 'center',
        width: '100%',
        alignSelf: 'center',
        padding: '4%',
        position: 'relative',
    },
    textButton: {
        color: '#fff',
        fontSize: 17
    },
    centeredView: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor:'background-color: rgba(0, 0, 0, 0.4);'
      },
      modalView: {
        // margin: 20,
        backgroundColor: "white",
        borderRadius: 20,
        padding: 35,
        // alignItems: "center",
        shadowColor: "#000",
        shadowOffset: {
          width: 0,
          height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
        width: '90%'
      },
      input: {
        width: '100%',
        borderWidth: 1,
        padding: 10,
        borderColor:'#fff',
        borderRadius: 30,
        elevation:2,
        backgroundColor: '#fff',
        color: '#000'
      },
      modalText:{
        fontSize:20,
        marginBottom:'5%',
        textAlign:'center'
      },
      cancelar:{
        backgroundColor:'#b94343',
        padding:15,
        borderRadius:30,
        width:"45%"
      },
      voltar:{
        backgroundColor:'rgba(0, 118, 193, 1)',
        padding:15,
        borderRadius:30,
        width:"45%"
      },
      textoBotao:{
        color:'#fff',
        fontWeight:'bold',
        textAlign:'center'
      }
});

export default QrViatura
