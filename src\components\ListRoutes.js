import React, {useState, useContext, useRef, useEffect} from 'react';
import {AppState,Alert,Button, Modal,StyleSheet,Pressable, TouchableOpacity} from 'react-native';
// import { Picker } from '@react-native-community/picker';
import {

  View,
  Text,
  StatusBar,
  ScrollView,
  TextInput,
  VirtualizedList,
  FlatList,
} from 'react-native';

import {useNavigation} from '@react-navigation/native';
import MapaFazendas from '../components/MapaFazendas';

import {Icon} from 'react-native-elements';

import {Dimensions} from 'react-native';
import axios from 'axios';
import {URL_API} from '../services/urls';
import MapaFazenda from './MapaFazenda';
import DatePicker from 'react-native-date-picker'
import ViaturaCard from './ViaturaCard';


const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

function ListRoutes(props) {
  const [date, setDate] = useState(new Date())
  const [date1, setDate1] = useState("Select a date")
  const [open, setOpen] = useState(false)
  const [open1, setOpen1] = useState(false)
  const [data, setData] = useState("")
  const [Area, setArea] = useState("")
  const [state, setState] = useState(0)
  const navigation = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);

  const Populate=(id)=>{
    axios.post(URL_API + 'buscar_viatura_atuacao',{id:id}).then(res => setData(res.data)).catch(err=>console.log(err))
  }

  // useEffect(() => {
  //   console.log(date)
  // }, [date])
  // useEffect(() => {
  //   console.log("THIS IS THE DATA BEING SET",data)
  // }, [data])


  useEffect(() => {
    if(Area){
      console.log("THIS IS THE AREA_ID FROM THE TODOS ROTAS", Area)
    }
      
  }, [Area])


  const Item = ({item}) => (
    <>
      <View style={styles.main}>
        <View style={styles.card}>
          <View style={styles.row}>
            <MapaFazenda/>
            <View style={{width:'50%'}}>
              <View style={styles.dados}>
                <Text style={styles.titulo}>Rota #{item.id}</Text>
                <View style={styles.row}>
                  <Text>Fazendas: </Text>
                  <Text style={styles.dado}>
                    {' '}
                    {item.lista_fazendas}
                  </Text>
                </View>
                <View style={styles.row}>
                  <Text>Kms Rodados: </Text>
                  <Text style={styles.dado}> {item.kilometragem_estimada}</Text>
                </View>
                <View style={styles.row}>
                  <Text>Área de atuação:</Text>
                  <Text style={styles.dado}>
                    {' '}
                    {item.nome_da_area}
                  </Text>
                </View>
              {
              item.id_viat? 
              <View style={styles.row}>
                <TouchableOpacity onPress={()=>{setModalVisible(!modalVisible),setArea(item.id_area)}} style={styles.button}>
                  <Text style={styles.textButton}>Vincular Viatura</Text>
                </TouchableOpacity>
              </View > 
              :
              <View style={styles.row}>
                  <Icon style={{marginRight: 15}} name="visibility" type="material" size={30} />
                  <Icon style={{marginRight: 15}} name="edit" type="material " size={30} />
                  <Icon style={{marginRight: 15}} name="delete" type="material"  size={30} />
              </View> 
              }
              </View>
            </View>
          </View>
          <View>
            {/* <View style={styles.cardDetalhes}>
                        <TouchableOpacity>
                            <Text>Detalhes da Fazenda</Text>
                        </TouchableOpacity>
                        <TouchableOpacity>
                            <Text>Excluir</Text>
                        </TouchableOpacity>
                    </View> */}
          </View>
        </View>

      </View>
    </>
  );

  return (
    <View >
      <View style={styles.header}>
        <TextInput
          style={styles.input}
          //onChangeText={onChangeText}
          value={'Pesquisar'}
          inlineImageLeft="search_icon"
        />
        <Icon style={{marginRight: 10}} name="tune" type="material" size={35} />
      </View>
      <View>
      <Text style={styles.info}>
          Confira a lista de rotas e clique se desejar ver mais detalhes:
        </Text>
        
        <FlatList
          style={{marginBottom:300}}
          data={props.data}
          renderItem={Item}
          keyExtractor={item => item.id_fzd}
        />
      </View>
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        
      >
        <TouchableOpacity activeOpacity={1} onPress={()=>{setModalVisible(false);setDate1("Select a date");setState("")}} style={{height:'100%',backgroundColor:'rgba(0, 0, 0, 0.5)'}}>
          <TouchableOpacity activeOpacity={1} style={{height:'70%',marginTop:"59%"}} onPress={console.log("hello")}>
            <View style={styles.centeredView}>
              <View style={styles.modalView}>
                <Text style={styles.modalText}>Insira as informacoes iniciais da roata para continuar</Text>
                <Text style={styles.modalText}>Data e hora de partida</Text>
                <View style={{flexDirection:'row',width:'100%'}}>
                <DatePicker
                    modal
                    mode="date"
                    open={open}
                    date={date}
            
                    onConfirm={(date) => {
                      setOpen(false)
                      var date1=JSON.stringify(date).split("T")[0]
                      const datee=date1.split("-")[2]
                      const month=date1.split("-")[1]
                      const year=date1.split("-")[0].split('"')[1]
                      setDate1(datee+"/"+month+"/"+year)
                      setDate(date)
                    }}
                    onCancel={() => {
                      setOpen(false)
                    }}
                  />
                  <TouchableOpacity style={{width:"100%",height:70,textAlign:'center',justifyContent:'center'}} onPress={() => setOpen(true)}>
                    <Text style={[styles.input,{textAlign:'center'}]}>{date1}</Text>
                  </TouchableOpacity>
                </View>
                <Text style={styles.modalText}>Response de Viatura </Text>
                
                <View style={{width:"100%",borderRadius:40,alignItems:'center',height:70,borderWidth:1}}>
                  {/* <Picker
                  style={styles.input}
                    selectedValue={state}
                    style={{marginTop:10,height: 50, width: "90%",backgroundColor:'offwhite'}}
                    onValueChange={(e) =>
                      setState(e)
                    }>
                      <Picker.Item  label="Digite aqui" value=""/>
                      {
                        props.Police?props.Police.map((ele)=>(
                          <Picker.Item label={ele.nome} value={ele.id} />
                        )):(<Picker.Item label="NULL" value="" />)
                      } 
                  </Picker> */}
                </View>
                  
                <TouchableOpacity disabled={!state || date1==="Select a date"} onPress={()=>{setOpen1(true);setModalVisible(false);Populate(Area)}} style={{height:50,width:'80%',borderRadius:40,backgroundColor:'#0076C1'}}>
                  <Text style={{textAlign:'center',lineHeight:50,color:'white'}}>
                    Proceed
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableOpacity>
        </TouchableOpacity>
        </Modal>


        <Modal
        animationType="slide"
        transparent={true}
        visible={open1}
        
      >
        <TouchableOpacity activeOpacity={1} onPress={()=>{setOpen1(false)}} style={{height:'100%',backgroundColor:'rgba(0, 0, 0, 0.5)'}}>
          <TouchableOpacity activeOpacity={1} style={{height:'90%',marginTop:"22%"}} onPress={console.log("hello")}>
            <View style={styles.centeredView}>
              <View style={styles.modalView}>
                <Text style={styles.modalText}>Procure a viatura que deseja criar uma rota de visitas</Text>
                <View style={styles.header}>
                  <TextInput
                    style={[styles.input,{marginBottom:20}]}
                    //onChangeText={onChangeText}
                    placeholder={'Pesquisar'}
                    inlineImageLeft="search_icon"
                  />
                </View>
                <Text style={[styles.modalText,{color:"blue"}]}>clique na viatura para selecionar</Text>

                  <View style={{height:"65%",width:'100%'}}>
                      <ScrollView style={{height:'100%'}}>
                      {
                        data?data.map(ele=>(
                          <ViaturaCard date={date1} close={setOpen1} police={state} route={Area} data={ele}/>
                        )):<Text>Loading</Text>
                      }
                      </ScrollView>
                  </View>

                  <TouchableOpacity disabled={!state || date1==="Select a date"} onPress={()=>{setOpen1(false)}} style={{height:50,marginTop:20,width:'80%',borderRadius:40,backgroundColor:'#0076C1'}}>
                    <Text style={{textAlign:'center',lineHeight:50,color:'white'}}>
                      Fechar
                    </Text>
                  </TouchableOpacity>
              </View>
            </View>
          </TouchableOpacity>
        </TouchableOpacity>
        </Modal>
    </View>
  );
}
const styles = StyleSheet.create({
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    
  },
  main: {
    
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    
  },
  input1:{
    width:'40%',
    margin:5,
  },    
  inputt:{
    width:'80%',
    borderWidth:1,
    margin:5,
  },
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 22
  },
  modalView: {
    width:"100%",
    height:'100%',
    margin: 20,
    backgroundColor: "white",
    borderRadius: 20,
    padding: 35,
    justifyContent:'space-evenly',
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5
  },
  button: {
    borderRadius: 20,
    padding: 10,
    elevation: 2
  },
  buttonOpen: {
    backgroundColor: "#F194FF",
  },
  buttonClose: {
    backgroundColor: "#2196F3",
  },
  textStyle: {
    color: "white",
    fontWeight: "bold",
    textAlign: "center"
  },
  modalText: {
    marginBottom: 15,
    textAlign: "center"
  },
  info: {
    marginTop: 20,
    marginBottom: 20,
    textAlign: 'center',
    color: 'gray',
    width: '70%',
    alignSelf: 'center',
  },
  card: {
    flex: 1,
    height: 220,
    width: '95%',
    backgroundColor: '#fff',
    padding: 5,
    elevation: 2,
    borderRadius: 30,
    padding: 10,
    marginBottom: '5%',
  },
  row: {
    flexDirection: 'row',
    flexWrap:'wrap',
    alignItems:'center',
    marginTop:5
  },
  mapa: {
    borderWidth: 1,
    borderRadius: 30,
  },
  input: {
    flex: 1,
    height: 40,
    margin: 12,
    padding: 10,
    elevation: 2,
    borderRadius: 30,
    backgroundColor: '#fff',
    color: 'gray',
  },
  button: {
    backgroundColor: '#0076C1',
    borderRadius: 30,
    flex: 1,
    justifyContent: 'center',
  },
  textButton: {
    color: '#fff',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  dados: {
    flex: 1,
    flexDirection: 'column',
    width:'100%',
    justifyContent: 'flex-start',
    marginLeft: '5%',
  },
  titulo: {
    textAlign: 'left',
    marginBottom: '6%',
    fontWeight: 'bold',
  },
  dado: {
    color: 'gray',
    fontSize: 12,
    marginTop: '1%',
    width: '60%',
  },
  icon: {
    marginTop: '20%',
    marginHorizontal: '5%',
  },
  cardDetalhes: {
    height: 30,
    borderTopColor: 'gray',
    borderTopWidth: 2,
    marginTop: '2%',
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
});
export default ListRoutes;
