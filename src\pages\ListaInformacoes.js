
 import React,{useState,useEffect} from 'react';
 import {
   SafeAreaView,
   ScrollView,
   StatusBar,
   StyleSheet,
   Text,
   TouchableOpacity,
   View,
 } from 'react-native';
//import MapaFazenda from '../components/MapaFazenda';
import { useNavigation } from '@react-navigation/core';
import axios from 'axios';
import AsyncStorage from '@react-native-community/async-storage';
import { URL_API } from '../services/urls';

 
function ListaInformacoes() {

  const navigation = useNavigation();
  const [informacoes, setInformacoes] = useState([]);
  const [fazendas, setFazendas] = useState([])
  //const [proprietatios, setProprietatios] = useState([])


  const fetchInformacoes = async () => {
    const response = await axios.get(URL_API + "informacoes");
    return response.data;
  }

  const fetchFazendas = async () => {
    const response = await axios.get(URL_API + "todas_fazendas");
    return response.data;
  }

  // const fetchProprietarios = async () => {
  //   const response = await axios.get(URL_API + "selecionar_todos_proprietarios");
  //   return response.data;
  // }

  const joinResult = ( 
    informacoes,
    fazendas, 
    proprietarios,
    ) => {
        let newInformacoes = informacoes;
        
        newInformacoes = newInformacoes.map((informacao) => {
            const fazenda = fazendas.find((f) => informacao.id_fzd === f.id_fzd)
            //const proprietario = proprietarios.find((p) => informacao.id_prop === p.id_prop)
           
            return {
                ...informacao,
                fazendaNome: fazenda ? fazenda.nome_da_fazenda : '',
                fazendaLatitude: fazenda ? fazenda.latitude : '',
                fazendaLongitude: fazenda ? fazenda.longitude : '', 
                proprietarioNome: fazenda ? fazenda.nome_do_proprietario : ''
            }
        })
        setInformacoes(newInformacoes);    
  }

  useEffect(()=>{
    Promise.all([
      fetchInformacoes(),
      fetchFazendas(),
      //fetchProprietarios(),
    ]).then((result)=> {
      const [
        informacoes,
        fazendas,
        proprietarios,
      ] = result;
      //setFazendas(fazendas);
      joinResult(informacoes, fazendas,proprietarios);
    })
  },[])
 

  return (
    <SafeAreaView style={styles.main}>
      <StatusBar />
      <ScrollView>
        <View>
          <Text style={styles.info}>Confira a lista de Informações</Text>
        </View>
          {informacoes.map((item, index) => {
            return (
              <TouchableOpacity onPress={() => navigation.navigate('detalheInformacao', {item})} style={styles.card}>
                <View  style={styles.option}>
                  <Text>#{item.id}</Text>
                  <Text>Fazenda: {item.fazendaNome}</Text>
                  <Text>Proprietário: {item.proprietarioNome}</Text>
                </View>
              </TouchableOpacity>
            )
          })}
      </ScrollView>
    </SafeAreaView>
  );

}

  const styles = StyleSheet.create({
   
    main:{
      flex:1,
      backgroundColor: '#fff'
    },

    info:{
        marginTop:'5%',
        marginBottom:'5%',
        textAlign: 'center',
        color: 'gray',
        width: '70%',
        alignSelf: 'center'
    },
    card:{
      backgroundColor: '#fff',
      width:'100%',
      height:'auto',
      borderRadius: 30,
      elevation: 1,
      marginBottom: '5%',
    },
    option:{
      backgroundColor:'#fff',
      marginLeft:'5%',
      // marginRight:'0%',
      marginTop:'2%',
      marginBottom:'2%',
      padding:5,
      borderRadius:5,
    },
  });
  
 
 export default ListaInformacoes;

 