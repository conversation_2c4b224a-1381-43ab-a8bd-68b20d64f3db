import React, {useState, useEffect} from 'react';
import { View, TouchableOpacity, StyleSheet, Text, Alert, ActivityIndicator } from 'react-native'
import { useUserContext } from '../context/AuthContext'
import { useLocation } from '../context/LocationContext'
import { saveGeolocation } from '../api'
import axios from 'axios'; 
import { useSocketContext } from '../context/socketContext';
import { Dimensions } from 'react-native';
import AsyncStorage from '@react-native-community/async-storage';
import {URL_SOCKET} from '../services/urls'

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

function BotaoEnviarViatura() {
  
  return (
      <View style={styles.main}>
        <TouchableOpacity style={styles.panicButton}>
            <Text style={styles.textButton}>ENVIAR VIATURA MAIS PRÓXIMA</Text>
        </TouchableOpacity>
      </View>
  );
}

const styles = StyleSheet.create({
  main:{
    flex: 1,
    alignItems: 'center'
  },
  panicButton: {
    padding: 15,
    backgroundColor: '#b94343',
    borderRadius: 30,
    marginTop: 15,
    width: width-70,
  },
  panicButtonDisabled: {
    padding: 15,
    backgroundColor: "gray",
    borderRadius: 30,
    marginTop: 15,
    width: width-50,
  },
  textButton: {
    color: 'white', 
    fontSize: 13,
    textAlign: 'center',
  }
});

export default BotaoEnviarViatura;