import React, { useState, useContext, useRef, useEffect } from 'react';
import { Alert, AppState, StyleSheet, TouchableOpacity } from "react-native";
import {
  View,
  Text,
  FlatList,
} from 'react-native';

import { useNavigation, useRoute } from '@react-navigation/native';
import { Icon } from 'react-native-elements';
import { Dimensions } from 'react-native'
import axios from 'axios';
import { URL_API } from '../../services/urls';
import moment from 'moment'
import OcorrenciaGetMotivosOcorrencia from './OcorrenciaGetMotivosOcorrencia';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;


function HistoricoOcorrencias() {

  const navigation = useNavigation();

  const route = useRoute();
  const id = route.params.id

  const [ocorrencias, setOcorrencias] = useState()
  const [motivos, setMotivos] = useState()
  const [loading, setLoading] = useState(false)

//   const getMotivos = () => {
//     // axios.get(URL_API+'motivos_ocorrencia')
//     //     .then(res => {
//     //         setMotivos(res.data.Motivos)
//     //     })
//     //     .catch(err => {
//     //         console.log(err)
//     //     })
//   }

  useEffect(()=>{
      setLoading(true)
      axios.get(URL_API+'ocorrencias_fazenda?id_fzd='+id)
      .then((res)=>{
        const visita = res.data
        // getMotivos()
        OcorrenciaGetMotivosOcorrencia(setMotivos)
        let ocorrencias = []

        if(motivos !== undefined){
            visita.map(data => {
                let motivo_detail = motivos.find(motivo => {
                    return motivo.id === data.id_motivo
                })
                console.log(motivo_detail)
                ocorrencias.push({
                    id: data.id,
                    id_motivo: data.id_motivo,
                    descricao: data.descricao,
                    observacao: data.observacao,
                    status: data.status,
                    created_at: data.created_at,
                    updated_at: data.updated_at,
                    motivo: motivo_detail.descricao
                })
            })
            setOcorrencias(ocorrencias)
            setLoading(false);
        }
        
      })
      .catch(res=>{
          Alert.alert('Aviso','Esta fazenda não possui ocorrências!')
      })
  },[motivos == undefined])
 const Item = ({ item }) => (
   <>
      
    <View style={styles.card}>
        <View style={{marginTop:'2%', marginLeft:'2%', flex:1}}>
            <Text style={styles.titulo}>Ocorrência #{item.id}</Text>
            <View style={styles.row}>
                <Text style={styles.tipo}>Data da Visita: </Text>
                <Text style={styles.dado}>{moment(item.created_at).format('DD/MM/YYYY hh:mm:ss')}</Text>
            </View>
            <View style={styles.row}>
                <Text style={styles.tipo}>Descrição: </Text>
                <Text style={styles.dado}>{item.descricao}</Text>
            </View>
            <View style={styles.row}>
                <Text style={styles.tipo}>Status: </Text>
                <Text style={styles.dado}>{item.status}</Text>
            </View>
            <Text style={styles.status}>Motivo: {item.motivo}</Text>
        </View>
    </View>

  </>
 
                     
   );
    
  
  return (
        <>
            <View style={styles.blueCard}>
                
            </View>
            <View style={styles.main}>
                <TouchableOpacity style={styles.panicButton} onPress={()=> navigation.navigate('ocorrencia',{id_fazenda: id})}>
                <Icon
                    name='add-circle-outline'
                    type='material'
                    color='#3F98D0'
                    style={styles.icon}
                    size={45}
                />
                </TouchableOpacity>
                <FlatList
                    data={ocorrencias}
                    renderItem={Item}
                    keyExtractor={item => item.id}
                />
            </View>
        </>
  );
}
const styles = StyleSheet.create({
    main:{
        flex:1,
        backgroundColor: '#fff',
        justifyContent: 'flex-end',
        borderTopLeftRadius:40,
        borderTopRightRadius:40
    },
    blueCard:{
        backgroundColor:'rgba(0, 118, 193, 1)',
        height:'6%'
    },
    card:{
        flex:1,
        height: 220,
        width:'95%',
        backgroundColor:'#fff',
        padding: 5,
        elevation:2,
        borderRadius:30,
        padding: 10,
        marginTop: '2.5%',
        marginBottom: '2.5%',
        alignSelf:'center'
    },
    row:{
        // flex:1,
        flexDirection: 'row',
        marginVertical: '2%'
    },
    Text:{
        marginTop:'5%',
        textAlign:'center',
        marginBottom:'2%',
        color:'#6D6D6D'
    },
    titulo:{
        fontSize:20,
        fontWeight:'bold'
    },
    tipo:{
        fontSize:16
    },
    dado:{
        fontSize:16,
        color: '#6D6D6D'
    },
    status:{
        fontSize: 16,
        fontWeight:"bold",
        color: 'rgba(0, 118, 193, 1)',
        marginTop:'5%'
    }
  });
export default HistoricoOcorrencias;