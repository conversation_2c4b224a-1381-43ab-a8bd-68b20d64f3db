import React, { useState, useContext, useRef, useEffect } from 'react';
import { AppState, StyleSheet, TouchableOpacity } from "react-native";
import {
  View,
  Text,
  StatusBar,
  ScrollView,
  TextInput,
  VirtualizedList
} from 'react-native';

import { useNavigation } from '@react-navigation/native';

import { Dimensions } from 'react-native'
import MapaVisitas from '../../components/MapaVisitas';
import ListaFazendasVisitas from '../../components/ListaFazendasVisitas';
import AsyncStorage from '@react-native-community/async-storage';
import axios from 'axios';
import { URL_API } from '../../services/urls';
import { useFetchValidacaoVisita } from '../../context/fetchValidacaoVisita';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;


function Visitas() {

  const navigation = useNavigation();

  const handleValidar = useFetchValidacaoVisita();

  const [telaTab, setTelaTab] = useState('visitar');
  const [fazendas, setFazendas] = useState({});
  const [fazendasTotal, setFazendasTotal] = useState({});
  const [rota, setRota] = useState(0);
  const [loading, setLoading] = useState(true);
  const [fazendasVisitadas, setFazendasVisitadas] = useState({});
  const [fazendasAvisitar, setFazendasAvisitar] = useState({});

  useEffect(()=>{
    async function getRota(){
        const rota = await AsyncStorage.getItem('@IdRota') 
        //console.log('rota',rota)
        setRota(Number(rota))
        
    }
    getRota()
    
  }, [])

  useEffect(() => { 
    getFazendasRotas();
  }, [rota, telaTab])

  const configurarDadosParaARota = (data) =>{

        const lista_fazendas = data.fazendas;
        const fazendas_visitadas = data.fazendas_visitadas

        const fazendas = lista_fazendas.filter((fazenda) =>{
            return !fazendas_visitadas.some((fazenda_visitada) => {
                return fazenda.id_fzd == fazenda_visitada.fzd_id
            })
        })

        const lista_visitadas = lista_fazendas.filter((fazenda) => {
            return fazendas_visitadas.some((fazenda_visitada) => {
                return fazenda.id_fzd == fazenda_visitada.fzd_id
            })
        })
        
        setFazendasTotal(lista_fazendas);
        setFazendasVisitadas(lista_visitadas);
        setFazendasAvisitar(fazendas);

        telaTab == 'visitar'
        ?
            (setFazendas(fazendas))
        :
            (setFazendas(lista_visitadas))

        setLoading(false);
    }

  const getFazendasRotas = () => {

    const data={
        id:rota
    }

    axios.post(URL_API+'buscar_rota', data)
    .then(async res=>{
        configurarDadosParaARota(res.data);
    
        await AsyncStorage.setItem('@Rota', JSON.stringify(res.data));
    
    })
    .catch(async err=>{

        await AsyncStorage.getItem('@Rota')
        .then(res => {
            configurarDadosParaARota(JSON.parse(res));
        })
        console.log('errorVisitas',err)
    })
  }

  return (
    <View style={styles.main}>
      <StatusBar barStyle={'light-content'} />
        <ScrollView contentInsetAdjustmentBehavior="automatic">
            <View style={styles.blueCard}>
            </View>
            <View style={styles.card}>
                <View style={styles.HeaderMap}>
                    <TouchableOpacity onPress={()=>setTelaTab('visitar')}>
                        <Text style={telaTab=='visitar'? styles.tituloOn : styles.tituloOff}>
                            Á Visitar ({fazendasAvisitar.length})
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>setTelaTab('visitada')}>
                        <Text style={telaTab=='visitar'? styles.tituloOff : styles.tituloOn}>
                            Visitado ({fazendasVisitadas.length})
                        </Text>
                    </TouchableOpacity>
                </View>
                {loading ?<></> : <MapaVisitas fazendas={fazendas}/> }
            </View>
            <Text style={styles.info}>Confira a lista de fazendas da rota</Text>
            <ListaFazendasVisitas tipo={telaTab} fazendasAvisitar={fazendasAvisitar} fazendasVisitadas={fazendasVisitadas} fazendasTotal={fazendasTotal} loading={loading}/>
        </ScrollView>
        <TouchableOpacity style={styles.buttonValidar} onPress={() => navigation.navigate('Qr',{
            fazendas: fazendasTotal
        })}>
            <Text style={styles.textButton}>Validar visita</Text>
        </TouchableOpacity>
    </View> 
  );
}
const styles = StyleSheet.create({
    main:{
        flex:1,
        backgroundColor: '#fff'
    },
    blueCard:{
        flex:1,
        width:'100%',
        height: 120,
        backgroundColor:'rgba(0, 118, 193, 1)',
        padding: 5,
        marginTop:'0%'
    },
    title:{
        color: '#fff',
        textAlign: 'left',
        marginLeft: '5%',
        fontSize: 20,
        fontWeight: 'bold'
    },
    info:{
        textAlign: 'center',
        color: 'gray',
        width: '70%',
        alignSelf: 'center'
    },
    input: {
        height: 40,
        margin: 12,
        padding: 10,
        elevation: 2,
        borderRadius: 30,
        backgroundColor: '#fff',
        color: 'gray',
    },
    titulo:{
        textAlign:'left',
        marginLeft: '5%',
        marginTop:'4%',
        fontSize: 15,
        marginBottom:'5%'
    },
    card:{
        backgroundColor: '#fff',
        width: '100%',
        height:400,
        marginTop: height-800,
        borderRadius: 30,
        elevation: 1,
        marginBottom: '10%',
    },
    HeaderMap:{
        justifyContent:'space-around',
        flexDirection:'row',
        padding:'3%',
    },
    tituloOn:{
        fontWeight:'bold',
        fontSize:15,
        color:'rgba(0, 118, 193, 1)'
    },
    tituloOff:{
        fontWeight:'bold',
        fontSize:15,
        color:'gray'
    },
    buttonValidar:{
        backgroundColor:'rgba(0, 118, 193, 1)',
        alignItems:'center',
        width:'100%',
        alignSelf: 'center',
        padding: '3%',
        position:'relative',
    },
    textButton:{
        color:'#fff',
        fontSize:17
    }
  });
export default Visitas;