import React, {useEffect, useState} from 'react';
import {Keyboard, Text, View, TextInput, StyleSheet, Alert} from 'react-native';
import {Button} from 'react-native-elements';
import {useUserContext} from '../context/AuthContext'

function Login({navigation}) {

  const {handleCodigo, loading, user, bloqueado} = useUserContext();
  const [codigo, setCodigo] = useState();

  const handleEnviar = () =>{
      handleCodigo(codigo)
  }

  
  return (
    <View style={styles.containerView}>
      <View style={styles.loginScreenContainer}>
        <View style={styles.loginFormView}>
        <Text style={styles.logoText}>Digite o código que foi enviado por SMS para o número <Text style={styles.telefone}>{user.TELEFONE}</Text></Text>
          <TextInput
            placeholder="Código"
            placeholderTextColor="#c4c3cb"
            style={styles.loginFormTextInput}
            value={codigo}
            onChangeText= {codigo => setCodigo(codigo)}
            autoCapitalize="none"
            autoCorrect={false}
          />
          <Button
          buttonStyle={styles.loginButton}
          onPress={handleEnviar}
          title="Enviar"
          />
        </View>
      </View>
    </View>
  );
}

export default Login;

const styles = StyleSheet.create({
  containerView: {
    flex: 1,
    backgroundColor: '#fafafa',
    marginTop: -30,
  },
  loginScreenContainer: {
    flex: 1,
  },
  logoText: {
    fontSize: 20,
    fontFamily: 'System',
    fontWeight: '800',
    marginTop: 150,
    marginBottom: 30,
    textAlign: 'center',
    color: 'gray',
  },
  loginFormView: {
    flex: 1,
  },
  loginFormTextInput: {
    height: 43,
    fontSize: 14,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#eaeaea',
    backgroundColor: '#fafafa',
    paddingLeft: 10,
    marginLeft: 15,
    marginRight: 15,
    marginTop: 5,
    marginBottom: 5,
    color: "gray"
  },
  loginButton: {
    backgroundColor: '#0076c1',
    borderRadius: 5,
    width: '93%',
    marginTop: 20,
    marginLeft: 15,
    padding: 10,
  },
  telefone:{
    color: '#0076c1'
  }
});
