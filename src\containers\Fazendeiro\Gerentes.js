import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ScrollView
} from 'react-native';
import MapboxGL from "@react-native-mapbox-gl/maps";
import { Dimensions } from 'react-native'
import { useLocation } from '../context/LocationContext';
import { useAlertContext } from '../context/AlertDadosContext';
import * as Progress from 'react-native-progress';
import { Icon } from 'react-native-elements'
import { Button } from 'react-native-elements/dist/buttons/Button';
import { useNavigation } from '@react-navigation/native';


const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

function Gerentes() {

    const navigation = useNavigation();

  return (
    <>
      <View styles={styles.main}>
          <View style={styles.mainHeader}>
              <Text style={styles.text}>Gerentes Cadastrados (2)</Text>
              <TouchableOpacity onPress={() => navigation.navigate('cadGerente')}>
                <Icon
                    name='add-circle'
                    type='material'
                    color='#3F98D0'
                    style={styles.icon}
                    size={25}
                />
              </TouchableOpacity>
          </View>
          {/* <View style={styles.card}>
              <Text>Card</Text>
          </View> */}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  main:{
      flex:1
  },
  mainHeader:{
    flex:1,
    flexDirection:'row',
    justifyContent:'space-evenly',
    marginTop:'10%',
  },
  text:{
    fontSize:17
  },
  card:{
      backgroundColor:'#fff',
      elevation:1,
      width:'90%',
      alignSelf:'center',
      padding:18,
      borderRadius:20,
      marginBottom:'15%',
      marginTop:'2%'
  }
});

export default Gerentes;