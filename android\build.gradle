// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        googlePlayServicesVersion = "+"
        firebaseMessagingVersion = "21.1.0"
        buildToolsVersion = "31.0.0"
        minSdkVersion = 21
        compileSdkVersion = 31
        targetSdkVersion = 31
        ndkVersion = "21.4.7075529"

        // Custom Mapbox dependencies to avoid authentication issues
        rnmbglMapboxLibs = {
            implementation 'com.mapbox.mapboxsdk:mapbox-sdk-services:5.1.0'
            implementation('com.mapbox.mapboxsdk:mapbox-android-sdk:9.1.0') {
                exclude group: 'com.mapbox.mapboxsdk', module: 'mapbox-android-accounts'
            }
            implementation 'com.mapbox.mapboxsdk:mapbox-android-telemetry:6.1.0'
            implementation 'com.mapbox.mapboxsdk:mapbox-android-plugin-annotation-v9:0.8.0'
        }
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:3.5.3")
        classpath("com.google.gms:google-services:4.3.10")
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        mavenLocal()
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }

        google()
        mavenCentral()
        maven { url 'https://www.jitpack.io' }
    }

    configurations.all {
        resolutionStrategy {
            force 'com.mapbox.mapboxsdk:mapbox-android-sdk:9.1.0'
            eachDependency { details ->
                if (details.requested.group == 'com.mapbox.mapboxsdk' && details.requested.name == 'mapbox-android-accounts') {
                    details.useTarget group: 'com.mapbox.mapboxsdk', name: 'mapbox-android-sdk', version: '9.1.0'
                }
            }
        }
    }
}

// Apply Google Services plugin at the bottom as recommended
apply plugin: 'com.google.gms.google-services'
