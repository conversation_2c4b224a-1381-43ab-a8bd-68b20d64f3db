// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        googlePlayServicesVersion = "+"
        firebaseMessagingVersion = "21.1.0"
        buildToolsVersion = "31.0.0"
        minSdkVersion = 21
        compileSdkVersion = 31
        targetSdkVersion = 31
        ndkVersion = "21.4.7075529"

        // Override Mapbox dependencies to use versions that don't require Downloads Token
        rnmbglMapboxLibs = {
            implementation 'com.mapbox.mapboxsdk:mapbox-sdk-services:5.1.0'
            implementation 'com.mapbox.mapboxsdk:mapbox-android-sdk:8.6.7'
            implementation 'com.mapbox.mapboxsdk:mapbox-android-telemetry:6.1.0'
            implementation 'com.mapbox.mapboxsdk:mapbox-android-plugin-annotation-v8:0.8.0'
        }
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:3.5.3")
        classpath("com.google.gms:google-services:4.3.10")
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        mavenLocal()
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }

        google()
        mavenCentral()
        maven { url 'https://www.jitpack.io' }

        // Try Mapbox public repository
        maven {
            url "https://repo1.maven.org/maven2/"
        }
    }
}

// Apply Google Services plugin at the bottom as recommended
apply plugin: 'com.google.gms.google-services'
