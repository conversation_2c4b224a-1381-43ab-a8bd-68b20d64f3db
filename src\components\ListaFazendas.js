import React, { useState, useContext, useRef, useEffect } from 'react';
import { AppState, StyleSheet, TouchableOpacity } from "react-native";
import {
  View,
  Text,
  StatusBar,
  ScrollView,
  TextInput,
  VirtualizedList,
  FlatList
} from 'react-native';

import { useNavigation } from '@react-navigation/native';
import MapaFazendas from '../components/MapaFazendas';

import { Icon } from 'react-native-elements';

import { Dimensions } from 'react-native'
import axios from 'axios';
import { URL_API } from '../services/urls';
import MapaFazenda from './MapaFazenda';
import { template } from '@babel/core';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;



function ListaFazendas() {

  const navigation = useNavigation();

  const [fazendas, setFazendas] = useState();

  useEffect(()=>{
      console.log('Chamada')
      axios.get(URL_API+'todas_fazendas')
      .then(res=>{
        console.log('gui', res)
        setFazendas(res.data)
      })
    },[])

  const Item = ({ item }) => (
    <>
        <View style={styles.main}>
            <View style={styles.card}>
                <View style={styles.row}>
                        <MapaFazenda lati={item.latitude} long={item.longitude} id={item.id_fzd}/>
                    <View style={styles.dados}>
                        <Text style={styles.titulo}>Fazenda #{item.codigo_da_fazenda}</Text>
                        <View style={styles.row}>
                            <Text>Data criação:</Text>
                            <Text style={styles.dado}> {item.data_do_cadastro}-{item.hora_do_cadastro}</Text>
                        </View>
                        <View style={styles.row}>
                            <Text>Apelido:</Text>
                            <Text style={styles.dado}> {item.nome_da_fazenda}</Text>
                        </View>
                        <View style={styles.row}>
                            <Text>Localização:</Text>
                            <Text style={styles.dado}> {item.latitude}, {item.longitude}</Text>
                        </View>
                        <View style={styles.row}>
                            <TouchableOpacity style={styles.button}>
                                <Text style={styles.textButton}>Agendar Visita</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
                <View>
                    {/* <View style={styles.cardDetalhes}>
                        <TouchableOpacity>
                            <Text>Detalhes da Fazenda</Text>
                        </TouchableOpacity>
                        <TouchableOpacity>
                            <Text>Excluir</Text>
                        </TouchableOpacity>
                    </View> */}
                </View>
            </View>
        </View>
    </>
    
                        
    );
    
  
  return (
        <>
            <View>
                <TextInput
                    style={styles.input}
                    //onChangeText={onChangeText}
                    value={'Pesquisar'}
                    inlineImageLeft='search_icon'
                />
            </View>
            <View>
                <FlatList
                    data={fazendas}
                    renderItem={Item}
                    keyExtractor={item => item.id_fzd}
                />
                    
                </View>
        </>
  );
}
const styles = StyleSheet.create({
    main:{
        flex:1,
        backgroundColor: '#fff',
        alignItems: 'center'
    },
    card:{
        flex:1,
        height: 220,
        width:'95%',
        backgroundColor:'#fff',
        padding: 5,
        elevation:2,
        borderRadius:30,
        padding: 10,
        marginBottom: '5%'
    },
    row:{
        flex:1,
        flexDirection: 'row',
        justifyContent:'flex-start'
    },
    mapa:{
        borderWidth:1,
        borderRadius:30
    },
    input: {
        height: 40,
        margin: 12,
        padding: 10,
        elevation: 2,
        borderRadius: 30,
        backgroundColor: '#fff',
        color: 'gray',
    },
    button:{
        backgroundColor:'#0076C1',
        borderRadius: 30,
        flex: 1,
        justifyContent: 'center',
    },
    textButton:{
        color: '#fff',
        textAlign: 'center',
        fontWeight:'bold'
    },
    dados:{
        flex:1,
        flexDirection:'column',
        justifyContent: 'flex-start',
        marginLeft: '5%'
    },
    titulo:{
        textAlign: 'left',
        marginBottom: '6%',
        fontWeight: 'bold'
    },
    dado:{
        color: 'gray',
        fontSize:12,
        marginTop: '1%',
        width:'60%'
    },
    icon:{
        marginTop:'20%',
        marginHorizontal: '5%'
    },
    cardDetalhes:{
        height:30,
        borderTopColor:'gray',
        borderTopWidth:2,
        marginTop:'2%',
        flexDirection:'row',
        justifyContent: 'space-around'
    }
  });
export default ListaFazendas;