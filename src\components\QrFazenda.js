import React, { useState, useContext, useRef, useEffect } from 'react';
import { AppState, StyleSheet, TouchableOpacity } from "react-native";
import {
  View,
  Text,
} from 'react-native';

import { useNavigation } from '@react-navigation/native';

import { Icon } from 'react-native-elements';

import { Dimensions } from 'react-native'
import axios from 'axios';
import { URL_API } from '../services/urls';
import QRCode from 'react-native-qrcode-svg';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;



function QrFazenda(props) {

  const navigation = useNavigation();

  const [gerado, setGerado] = useState(false);
  const [value, setValue] = useState();
  const [loading, setLoading] = useState();

  useEffect(()=>{
      setLoading(true)
      axios.get(URL_API+'todas_qrcodes')
      .then(res=>{
          setLoading(false)
          for (let i = 0; i < res.data.length; i++) {
              if(res.data[i].id_fzd == props.id){
                  setValue(res.data[i])
                  setGerado(true);

              } 
          }
      })
  },[])
  return (
    <>
    {loading 
      ?
      <></> 
      :
        <>
        {gerado &&
          <View style={styles.card}>
            <View style={{marginVertical:'2%'}}>
                <Text style={{fontWeight:'bold',color:'gray', textAlign:'center'}}>Gerado em</Text>
                <Text style={{fontSize:18}}>{value.data_da_criacao}</Text>
            </View>
            <View style={styles.div}>

            </View>
            <QRCode
                value={JSON.stringify(value.id_fzd)}
                style={styles.qr}
                size={250}
            />
          </View>
        }
        </>
    }
    </>
  );
}
const styles = StyleSheet.create({
    card:{
        backgroundColor:'#fff',
        alignItems:"center",
        marginVertical:'5%',
        padding:15,
        width:'85%',
        alignSelf:'center',
        borderRadius:30
    },
    qr:{
        width:100,
    },
    div:{
        borderBottomColor:'#E4E9EB',
        borderBottomWidth:2,
        width:"95%",
        height:2,
        alignSelf:'center',
        marginVertical:'3%'
    },
  });
export default QrFazenda;