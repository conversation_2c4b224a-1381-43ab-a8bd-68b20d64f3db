import React, {
} from 'react'
import {
  StyleSheet, View,
  Text,
  TouchableOpacity,
} from "react-native";
import { Icon } from 'react-native-elements'
import { useNavigation, useRoute } from '@react-navigation/native';


function CardEquipamento({ type, description, armed, id }) {

  const navigation = useNavigation()

  return (
    <View style={styles.mainCard}>
      <TouchableOpacity onPress={() => navigation.navigate('detalheequipamento', {equipamento_id: id})}>
        <View style={styles.card}>
          <View style={styles.texts}>
            <View>
              <Text style={styles.nome}>Nome do equipamento</Text>
              <Text style={styles.nomeContent}>{type}</Text>
              <Text style={styles.nome}>Descrição</Text>
              <Text style={styles.nomeContent}>{description}</Text>
              {armed == 1 ? <Text style={styles.nomeContent}>Pronto</Text> : <></>}
            </View>
          </View>
          <View>
            <Text style={styles.detalheText}>Detalhes do equipamento</Text>
            <Icon
              name='arrow-forward'
              type='material'
              color='#3F98D0'
              style={styles.icon}
              size={25}
            />
          </View>
        </View>
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor: '#fff'
  },
  blueCard: {
    flex: 1,
    width: '100%',
    height: 100,
    backgroundColor: 'rgba(0, 118, 193, 1)',
    padding: 5
  },
  title: {
    color: '#fff',
    textAlign: 'left',
    marginLeft: '5%',
    fontSize: 20,
    fontWeight: 'bold'
  },
  info: {
    textAlign: 'center',
    color: 'gray',
    width: '70%',
    alignSelf: 'center'
  },
  input: {
    height: 40,
    margin: 12,
    padding: 10,
    elevation: 2,
    borderRadius: 30,
    backgroundColor: '#fff',
    color: 'gray',
  },
  mainCard: {
    paddingTop: 10,
  },
  card: {
    backgroundColor: '#fff',
    width: '89%',
    flex: 1,
    alignSelf: 'center',
    elevation: 1,
    borderRadius: 30,
    marginTop: '-25%',
    padding: 15,
  },
  texts: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-around'
  },
  nome: {
    color: 'gray'
  },
  nomeContent: {
    fontSize: 17
  },
  detalhe: {
    flex: 1,
  },
  detalheText: {
    color: 'rgba(0, 118, 193, 1)',
    alignSelf: 'center',
    marginTop: '2%'
  }
});

export default CardEquipamento