import React, {useState, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Text,
  Alert,
  ActivityIndicator,
  Modal,
  TextInput
} from 'react-native';
import {useUserContext} from '../context/AuthContext';
import {useLocation} from '../context/LocationContext';
import {saveGeolocation} from '../api';
import axios from 'axios';
import {useSocketContext} from '../context/socketContext';
import {Dimensions} from 'react-native';
import AsyncStorage from '@react-native-community/async-storage';
import {URL_API, URL_SOCKET} from '../services/urls';
import {Icon} from 'react-native-elements';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

function BotaoPanico(props) {
  const {location} = useLocation();
  const [geolocationInterval, setGeolocationInterval] = useState(null);
  const [panico, setPanicos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  // Id do Alerta que estou emitindo
  const [idAlert, setIdAlert] = useState(0);
  const {socket} = useSocketContext();

  const {user} = useUserContext();

  //verifiva se o alerta que enviei está em andamento
  const [andamento, setAndamento] = useState(false);

  //verifiva se o Botão de Alerta foi está ativo
  const [ativo, setAtivo] = useState(true);
  const [id, setId] = useState();
  const [resumo, setResumo] = useState('');

  // console.log('iddddd',idAlert)

  const getStatus = () =>{
    axios.post(URL_API+'panico_id_fazenda',{
      id: props.nomeFZD
    })
    .then(res=>{
      console.log('dataaaaaa',res.data)
      setIdAlert(res.data[0].ID)
      // console.log('status',res.data[0].STATUS)     
      if(res.data == 'Não há nenhum pânico ativo na fazenda'){
        setAtivo(true);
      }
      if(res.data[0].STATUS){
        if(res.data[0].STATUS == 0){
          setAtivo(false);
        }
        if(res.data[0].STATUS == 1){
          setAndamento(true);
          setAtivo(false);
        }
      }
    })
    .catch(res=>{
      console.log(res)
    })
  }

  useEffect(()=>{
    getStatus()
  },[])

  const getId = async () => {
    const idAsync = await AsyncStorage.getItem('@Id');
    if(idAsync){
      setId(idAsync);
      // console.log('idAsyncIF', idAsync)
      
    } 
    else{
      setId(user.ID_USUARIO);
      // console.log('idAsyncElse', idAsync)
    }
  };
  useEffect(() => {
    getId();
  }, [ativo]);

  const acionaPanico = async () => {
    setLoading(true);

    const data = {
      latitude: location.latitude,
      longitude: location.longitude,
      hora: new Date(),
      user_id: id,
      tipo: '0',
      NOME_FAZENDA: props.nomeFZD,
      NOME_AREAFZD: props.nomeArea,
      PROPRIETARIO: id
    }
    console.log(data)
    return axios
      .post(URL_SOCKET + 'panico', data)
      .then(res => {
        console.log('ultimoId',res.data.last_id);
        if (res.data.resposta == 'Alerta acionado!') {
          setIdAlert(res.data.last_id); //Pega o id do Alerta
          setAtivo(false);
          if (!geolocationInterval) {
            saveGeolocation({...location, userId: id});
            //Salva localização a cada minuto
            setGeolocationInterval(
              setInterval(
                () => saveGeolocation({...location, userId: id}),
                60000,
              ),
            );
          }
          Alert.alert('Botão de pânico foi acionado!');
        } else {
          console.error(res);
          Alert.alert('O botão de pânico não foi acionado!!');
        }
      })
      .catch(err => {
        console.error(err);
        Alert.alert('O botão de pânico não foi acionado!!');
      })
      .finally(() => {
        setLoading(false);
      });
  };
  // quando o botão de cancelar alerta é pressionado
  const handleCancelarPanicoPress = () => {
    // console.log('idAlert',idAlert)
    axios
      .post(URL_SOCKET + 'panico_cancelada', {
        id: idAlert,
        resumo: resumo
      })
      .then(res => {
        Alert.alert('',`Chamado Cancelado!`);
        setModalVisible(!modalVisible)
        // console.log('certo',res)
      })
      .catch(res =>{
        Alert.alert('',res);
      })
      setAtivo(true);
    
  };

  // verifica se o meu alerta está em andamento
  useEffect(() => {
    console.log('socket', socket.connected);
    socket.on('Atenção viatura a caminho', data => {
      console.log('acaminho--------------------------------', data);
      //console.log('ID',data.ID)
      // console.log('id',idAlert)
      if (data.ID == idAlert) {
        setAndamento(true);
      }
    });

    socket.on('Atenção ocorrencia cancelada', data => {
      console.log('cancelada--------------------------------', data);
      // console.log(data.ID)
      if (data.ID == idAlert) {
        setAtivo(true);
        setAndamento(false);
      }
    });

    socket.on('Atenção ocorrencia finalizada', data => {
      console.log('finalizada', data);
      if (data.ID == idAlert) {
        setAtivo(true);
        setAndamento(false);
      }
    });
  }, [socket,idAlert]);

  return (
    <>
      {ativo ? (
        <TouchableOpacity
          style={styles.panicButton}
          onPress={() => acionaPanico()}>
          <Icon
            name="campaign"
            type="material"
            color="#FFF"
            style={styles.icon}
            size={45}
          />
          {loading ? (
            <ActivityIndicator color="#0000ff" />
          ) : (
            <Text style={styles.textButton}>Acionar Pânico</Text>
          )}
        </TouchableOpacity>
      ) : (
        <>
          {andamento ? (
            <TouchableOpacity
              style={styles.panicButtonDisabled}
              onPress={panico.STATUS == 1 ? handleCancelarPanicoPress : null}
              disabled={true}>
              <Icon
                name="campaign"
                type="material"
                color="#FFF"
                style={styles.icon}
                size={45}
              />
              {loading ? (
                <ActivityIndicator color="#0000ff" />
              ) : (
                <Text style={styles.textButton}>Cancelar Pânico</Text>
              )}
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.panicButton}
              onPress={() => setModalVisible(true)}
              disabled={andamento}>
              <Icon
                name="campaign"
                type="material"
                color="#FFF"
                style={styles.icon}
                size={45}
              />
              {loading ? (
                <ActivityIndicator color="#0000ff" />
              ) : (
                <Text style={styles.textButton}>Cancelar Pânico</Text>
              )}
            </TouchableOpacity>
          )}
        </>
      )}
      {/* Modal */}
      {/* <View style={styles.centeredView}> */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => {
            Alert.alert("Modal has been closed.");
            setModalVisible(!modalVisible);
          }}
        >
          <View style={styles.centeredView}>
            <View style={styles.modalView}>
              <Text style={styles.modalText}>Motivo do cancelamento</Text>    
              <TextInput
                    style={styles.input}
                    onChangeText={(e)=> setResumo(e)}
                    value={resumo}
                    placeholder="Digite aqui"
                    placeholderTextColor='gray'
                    maxLength={40}
                    numberOfLines={6}
                    multiline
                />
                <View style={{flexDirection:'row', marginTop:'5%', justifyContent:'space-between'}}>
                  <TouchableOpacity
                    style={styles.voltar}
                    onPress={() => setModalVisible(!modalVisible)}
                  >
                    <Text style={styles.textoBotao}>Voltar</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.cancelar}
                    onPress={() => handleCancelarPanicoPress()}
                  >
                    <Text style={styles.textoBotao}>Cancelar pânico</Text>
                  </TouchableOpacity>
                </View>
            </View>
          </View>
        </Modal>
    {/* </View> */}
    </>
  );
}

const styles = StyleSheet.create({
  panicButton: {
    padding: 15,
    borderRadius: 30,
    marginTop: '2%',
    width: '47%',
    backgroundColor: '#b94343',
    elevation: 1,
    // marginHorizontal: '2%'
  },
  textButton: {
    color: '#fff',
    fontSize: 15,
    textAlign: 'left',
  },
  panicButtonDisabled: {
    padding: 15,
    borderRadius: 30,
    marginTop: '2%',
    width: '47%',
    backgroundColor: 'gray',
    elevation: 1,
    // marginHorizontal: '2%'
  },
  icon: {
    alignItems: 'flex-start',
  },
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor:'background-color: rgba(0, 0, 0, 0.4);'
  },
  modalView: {
    // margin: 20,
    backgroundColor: "white",
    borderRadius: 20,
    padding: 35,
    // alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '90%'
  },
  input: {
    width: '100%',
    borderWidth: 1,
    padding: 10,
    borderColor:'#fff',
    borderRadius: 30,
    elevation:2,
    backgroundColor: '#fff',
    color: '#000'
  },
  modalText:{
    fontSize:20,
    marginBottom:'5%',
    textAlign:'center'
  },
  cancelar:{
    backgroundColor:'#b94343',
    padding:15,
    borderRadius:30,
    width:"45%"
  },
  voltar:{
    backgroundColor:'rgba(0, 118, 193, 1)',
    padding:15,
    borderRadius:30,
    width:"45%"
  },
  textoBotao:{
    color:'#fff',
    fontWeight:'bold',
    textAlign:'center'
  }
});

export default BotaoPanico;
