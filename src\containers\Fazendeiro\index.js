import React, { useState, useContext, useRef, useEffect } from 'react';
import { AppState, StyleSheet } from 'react-native';
import { View, Text, StatusBar, ScrollView } from 'react-native';
import { useLocation } from '../../context/LocationContext';
import { useUserContext } from '../../context/AuthContext';
import { usePermission } from '../../context/LocationPermissionContext';
import { Button } from 'react-native-elements';
import axios from 'axios';
import { URL_API } from '../../services/urls';


import Header from '../../components/Header';
import AsyncStorage from '@react-native-community/async-storage';

import { useNavigation } from '@react-navigation/native';
import BotaoPanico from '../../components/BotaoPanico';

import CardFazenda from '../../components/CardFazenda';
import BotaoListagem from '../../components/BotoesListagem';
import Gerentes from './Gerentes';



function Fazendeiro() {
  
  const navigation = useNavigation();
  const { location } = useLocation();
  const { requestLocationPermission, findCoordinatesFazendeiro } = usePermission();
  const appState = useRef(AppState.currentState);
  const [appStateVisible, setAppStateVisible] = useState(appState.current);
  const [nome, setNome] = useState();
  const [id, setId] = useState(0);
  
  const logout = async () => {
    await AsyncStorage.removeItem('@Id');
    await AsyncStorage.removeItem('@Identificacao');
    navigation.reset({
      index: 0,
      routes: [{ name: 'Login' }],
    });
  };

  const getUserId = async () => {
    const other=await AsyncStorage.getItem('@Id');
    setId(other);
  }

  useEffect(() => {
    (async () => {
      await requestLocationPermission();
      await findCoordinatesFazendeiro();
    })();
  }, []);

  useEffect(() => {

    const chamar = async() => {
      await getUserId();
      axios.post(URL_API + 'buscar_fazenda_proprietario', {
        id: id
        //id: 3
      })
      .then(res =>{
         setNome(res.data)
      })
      .catch(err => {
        console.log(err)
      })
    }
    chamar();    
  }, [id])


  // useEffect(() => {
  //   if (nome) console.log("IMIM", nome)
  // }, [nome])

  return (
    <View>
      <StatusBar barStyle={'light-content'} />
      <ScrollView contentInsetAdjustmentBehavior="automatic">
        <Header subtitle="Proprietário" />
        {
          nome ?
            nome.map((item) => (
              <CardFazenda p={nome?nome.indexOf(item):1} item={item} />
            ))
            :
            <>
              <View style={[styles.main, { backgroundColor: "rgba(0, 118, 193, 1)"}]}>
                <Text style={styles.text}>Nenhuma fazenda para mostrar</Text>
              </View>
            </>
        }

        {/* <BotaoListagem id={'FZD'}/> */}
        {/* <Gerentes /> */}
        <Button onPress={logout} title="Sair" />
        {/* {location &&
            <Text>Localização atual: {location.latitude}, {location.longitude}</Text>
          } */}
        </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  main: {
    padding: 15,
    height: 100,
    borderBottomLeftRadius: 50,
    borderBottomRightRadius: 50,
    flex: 1
  },
  text:{
    color:'#fff',
    textAlign:'center',
    fontWeight:'bold',
    fontSize:18
  }
})

export default Fazendeiro;
