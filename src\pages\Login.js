import React, {useEffect, useState} from 'react';
import {Keyboard, Text, View, TextInput, StyleSheet, Image, Alert, Linking} from 'react-native';
import {Button} from 'react-native-elements';
import * as Progress from 'react-native-progress';
import AsyncStorage from '@react-native-community/async-storage';
import {useUserContext} from '../context/AuthContext'
import axios from 'axios';
import { URL_API } from '../services/urls';
import {useSocketContext} from '../context/socketContext'
import PushNotification from "react-native-push-notification";
import Logo from '../assets/AIBA.jpg'

function Login({navigation}) {

  const [email, setEmail] = useState();
  const [password, setPassword] = useState();
  const [token, setToken] = useState();

  const {handleUserLogin, loading, bloqueado, user} = useUserContext();

  //pega o token de autenticação e guarda no AsyncStorage
  async function getToken(){
    await axios.get(URL_API+'api')
    .then((response)=>{
      setToken(response.data.key);
      console.log(response.data.key)
      AsyncStorage.setItem('@Token', response.data.key);
    })
    .catch((err)=>{
      console.log('erroToken',err );
    })
    
  }
  //verifica se o user tem o id armazenado no cache do celular, se não tiver fica na tela de login
  useEffect(() => {
    getToken()
  }, [])

  useEffect(()=>{
    const auth = async() =>{
      console.disableYellowBox = true;
      //Mudar aqui para entrar direto com a ident que deseja
      // const id = (await AsyncStorage.getItem('@Identificacao')).slice(1,-1);
      // const id = (await AsyncStorage.getItem('@Identificacao')).replace('"',' ').replace('"',' ').trim();
      
      const id = JSON.parse(await AsyncStorage.getItem('@Identificacao'))
      const name = await AsyncStorage.getItem('@Nome');
      const other = await AsyncStorage.getItem('@Id');
      //const id = 'FZD';
      
      const viatura = await AsyncStorage.getItem('@Viatura');
      console.log("THIS IS IDDDDD",id,name,other)
      if(id){
        console.log("idLogin",id)
        switch (id){
          case 'PM':
            if(viatura){
              navigation.reset({
                index: 0,
                routes: [{ name: 'Policial' }],
              });
            }
            else{
              navigation.reset({
                index: 0,
                routes: [{ name: 'IniciarServico' }],
              });
            }
            break;
          case 'COORDPM':
            navigation.reset({
              index: 0,
              routes: [{ name: 'Coord' }],
            });
            break;
          case 'FZD':
          navigation.reset({
            index: 0,
            routes: [{ name: 'Fazendeiro' }],
          });
          break;
          default:
            console.log("object")
            break;
        }
      }
      else{
        console.log("SUB GOLMAAL HA")
      }
    }
    auth();
  },[]);

  async function handleSign() {
    if(!email){
      Alert.alert('Campo de email obrigatório')
      return
    }
    if(!password){
      Alert.alert('Campo de senha obrigatório')
      return
    }
    const response = await handleUserLogin(email, password, token);
    // Mensagem via sms
    // if( response == 'OK'){
    //   navigation.reset({
    //     index: 0,
    //     routes: [{ name: 'SMS' }],
    //   });
    // }
  }
  
  return (
    <View style={styles.containerView}>
      <View style={styles.loginScreenContainer}>
        <View style={styles.loginFormView}>
          <Image
            style={styles.logo}
            source={Logo}
          />
          {loading ? (
            <Progress.CircleSnail
              style={styles.progressCircle}
              color={['#0076c1']}
              size={50}
              indeterminate={true}
            />
          ) : (
            <>
              <TextInput
                placeholder="Usuários"
                placeholderTextColor="gray"
                style={styles.loginFormTextInput}
                value={email}
                onChangeText={email => setEmail(email)}
                autoCapitalize="none"
              />
              <TextInput
                placeholder="Senha"
                placeholderTextColor="gray"
                style={styles.loginFormTextInput}
                value={password}
                onChangeText= {password => setPassword(password)}
                autoCapitalize="none"
                autoCorrect={false}
                secureTextEntry
              />
              <Button
                buttonStyle={styles.loginButton}
                onPress={handleSign}
                title="Entrar"
                disabled={bloqueado}
              />
              {/* <Button
                buttonStyle={styles.loginforget}
                type="clear"
                title="Esqueceu a Senha?"
                titleStyle={styles.titleLoginForget}
                size={5}
              /> */}
            </>
          )}
        </View>
        <Text style={{marginBottom: 10, textAlign: 'center', color: "#0000EE"}} onPress={() => Linking.openURL("https://aiba-web-integracao.herokuapp.com/terms")}>Clique aqui para acessar nossa política de privacidade</Text>
      </View>
    </View>
  );
}

export default Login;

const styles = StyleSheet.create({
  containerView: {
    flex: 1,
    backgroundColor: '#ffff',
    marginTop: -30,
  },
  loginScreenContainer: {
    flex: 1,
  },
  logoText: {
    fontSize: 40,
    fontFamily: 'System',
    fontWeight: 'bold',
    marginTop: 150,
    marginBottom: 30,
    textAlign: 'center',
    color: '#0076c1',
  },
  loginFormView: {
    flex: 1,
  },
  loginFormTextInput: {
    height: 43,
    fontSize: 14,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#eaeaea',
    backgroundColor: '#fafafa',
    paddingLeft: 10,
    marginLeft: 15,
    marginRight: 15,
    marginTop: 5,
    marginBottom: 5,
    color: "gray"
  },
  progressCircle: {
    alignSelf: 'center',
  },
  loginButton: {
    backgroundColor: '#0076c1',
    borderRadius: 5,
    width: '93%',
    marginTop: 10,
    marginLeft: 15,
    padding: 10,
  },
  loginforget: {
    //marginLeft: 190,
    textAlign: 'center',
    marginTop: 10,
    width: 170,
    backgroundColor: 'transparent',
  },
  titleLoginForget: {
    color: '#31788A',
  },
  logo:{
    width:390,
    height:95,
    marginTop:150,
    marginBottom:30,
    alignSelf:"center"
  }
});
