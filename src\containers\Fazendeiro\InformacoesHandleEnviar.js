import {Alert} from 'react-native';
import { URL_API } from '../../services/urls';


const InformacoesHandleEnviar = async (axios, navigation,motivoInformacao,descricao, idProprietario,id_fazenda,delegacia) => {
 
  if (motivoInformacao === '' || motivoInformacao === undefined){
    Alert.alert('Selecione o motivo da informação')
    return
  }
      
  if (descricao === '' || descricao === undefined){
    Alert.alert('A Descrição deve ser preenchida')
    return
  } 

  return axios.post(URL_API + 'abrir_informacao', {
    id_prop:idProprietario.toString(),
    id_fzd: id_fazenda.toString(),
    id_motivo: motivoInformacao.toString(),
    descricao: descricao.toString(),
    registrado_fora_delegacia:delegacia.toString()
    
  })
    .then(res => {           
      Alert.alert('Informação enviada');
      navigation.navigate('Fazendeiro') 
      return res;
    })
    .catch(err =>{
      Alert.alert('Houve um erro ao enviar informação, tente novamente')
      return err;
    })
}

export default InformacoesHandleEnviar;