import React, { useState, useContext, useRef, useEffect } from 'react';
import { AppState, StyleSheet } from "react-native";
import {
  View,
  Text,
  StatusBar,
  ScrollView,
  TextInput,
  VirtualizedList
} from 'react-native';

import { useNavigation } from '@react-navigation/native';
import MapaFazendas from '../components/MapaFazendas';

import { Dimensions } from 'react-native'
import ListaFazendas from '../components/ListaFazendas';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;


function Fazendas() {

  const navigation = useNavigation();
  
  return (
    <View style={styles.main}>
      <StatusBar barStyle={'light-content'} />
        <ScrollView contentInsetAdjustmentBehavior="automatic">
            <View style={styles.blueCard}>
                <View>
                    <Text style={styles.title}>Fazendas Cadastradas</Text>
                </View>
            </View>
            <MapaFazendas/>
            <Text style={styles.info}>Confira a lista de fazendas e clique se desejar ver mais detalhes</Text>
            <ListaFazendas/>
        </ScrollView>
    </View> 
  );
}
const styles = StyleSheet.create({
    main:{
        flex:1,
        backgroundColor: '#fff'
    },
    blueCard:{
        flex:1,
        width:'100%',
        height: 100,
        backgroundColor:'rgba(0, 118, 193, 1)',
        padding: 5
    },
    title:{
        color: '#fff',
        textAlign: 'left',
        marginLeft: '5%',
        fontSize: 20,
        fontWeight: 'bold'
    },
    info:{
        textAlign: 'center',
        color: 'gray',
        width: '70%',
        alignSelf: 'center'
    },
    input: {
        height: 40,
        margin: 12,
        padding: 10,
        elevation: 2,
        borderRadius: 30,
        backgroundColor: '#fff',
        color: 'gray',
    },
  });
export default Fazendas;