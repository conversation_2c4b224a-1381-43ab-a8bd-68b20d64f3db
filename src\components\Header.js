import AsyncStorage from '@react-native-community/async-storage';
import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity
} from 'react-native';
import { useUserContext } from '../context/AuthContext';
import { Icon } from 'react-native-elements'

function Header({ subtitle }) {
  const imgURL = ""
  const{user} = useUserContext();

  const [nome, setNome] = useState();
  const [viatura, setViatura] = useState();
  const [responsavel, setResponsavel] = useState()

  //const nome = Nome;
  async function getNome(){
    const nomeAsync = await AsyncStorage.getItem('@Nome');
    if(nomeAsync){
      setNome(nomeAsync);
      //console.log('nomeAsyncIF', nomeAsync)
    } 
    else{
      setNome(user.NOME);
      //console.log('nomeAsyncElse', nomeAsync)
    }
  }
  async function getViatura(){
    if(subtitle == 'Policial'){
      const viatura = await AsyncStorage.getItem('@Viatura');
      const responsavel = await AsyncStorage.getItem('@Responsavel')
      setViatura(viatura);
      setResponsavel(responsavel)
    }
  }

  useEffect(() => {
    getNome();
    getViatura();
  },[])


  return (
    <>
      <View style={styles.main}>
        <View>
          <Image 
            style={styles.image} 
            source={require('../assets/user.png')}
          />
        </View>
        <View>
          {subtitle == 'Policial'
            ?
            <>
              <Text style={styles.nome}>Viatura #{viatura}</Text>
              {subtitle && <Text style={styles.subtitle}>Responsável: {responsavel}</Text>}
            </>
            :
            <>
              <Text style={styles.nome}>{nome}</Text>
              {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
            </>
          }
        </View>
        <View styles={styles.sair}>
          {/* <TouchableOpacity>
            <Icon
              name='logout'
              type='material'
              color='#fff'
            />
          </TouchableOpacity> */}
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  main: {
    backgroundColor: 'rgba(0, 118, 193, 1)',
    padding: 15,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center'
  },
  nome: {
    fontSize: 16,
    color: "#fff",
    fontWeight: '700'
  },
  subtitle: {
    color: "#fff",
    fontSize: 12,
  },
  image: {
    width: 50,
    height: 50,
    borderRadius: 50 / 2,
    overflow: "hidden",
    borderWidth: 3,
    borderColor: "#fff",
    marginRight: 20
  },
  sair:{
    marginRight: 100
  }
});

export default Header;