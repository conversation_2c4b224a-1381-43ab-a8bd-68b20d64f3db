import React from 'react'
import { LocationProvider } from './context/LocationContext';
//import { UserProvider } from '../src/context/AuthContext';
import {SocketProvider} from './context/socketContext';
//import { PermissionProvider } from '../src/context/LocationPermissionContext';
import { NavigationContainer } from "@react-navigation/native";
import { AlertProvider } from './context/AlertDadosContext';


const AppTest = ({children}) => {
  return (
    <NavigationContainer>
      <SocketProvider>
        {/* <UserProvider> */}
          <LocationProvider>
            {/* <PermissionProvider> */}
              <AlertProvider>
                {children}
              </AlertProvider>
            {/* </PermissionProvider> */}
          </LocationProvider>
        {/* </UserProvider>       */}
      </SocketProvider>
    </NavigationContainer>
  );
};

export default AppTest;