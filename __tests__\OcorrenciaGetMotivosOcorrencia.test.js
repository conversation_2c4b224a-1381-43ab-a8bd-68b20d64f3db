import 'react-native';
import OcorrenciaGetMotivosOcorrencia from '../src/containers/Fazendeiro//OcorrenciaGetMotivosOcorrencia';


it('Teste OcorrenciaGetMotivosOcorrencia Sucesso', async () => {
  
  const axios = {
    get: async () => {
      return 200
    }
  }
  const retorno = await OcorrenciaGetMotivosOcorrencia(axios)
  expect(200).toBe(200);

});

it('Teste OcorrenciaGetMotivosOcorrencia error ', async () => {
  
  const axios = {
    get: async () => {
      return 400
    }
  }
  const retorno = await OcorrenciaGetMotivosOcorrencia(axios)
  expect(400).toBe(400);
});

