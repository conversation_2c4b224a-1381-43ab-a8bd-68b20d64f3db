import React, { useState } from 'react';
import { AppState, StyleSheet, TouchableOpacity } from "react-native";
import {
  View,
  Text,
  FlatList,
  Linking
} from 'react-native';

import { Dimensions } from 'react-native'
import MapaFazenda from './MapaFazenda';

const window = Dimensions.get('window');



function ListaFazendasVisitas({tipo, fazendasAvisitar, fazendasVisitadas, fazendasTotal, loading}) {

  const Item = ({ item }) => (
    <>
        <View style={styles.main}>
            <TouchableOpacity style={styles.card} onPress={()=> {Linking.openURL('https://www.google.com/maps/dir/?api=1&destination='+item.latitude+','+item.longitude+'')}}>
                <View style={styles.row}>
                    <MapaFazenda lati={item.latitude} long={item.longitude} id={item.id_fzd}/>
                    <View style={styles.dados}>
                        <Text style={styles.titulo}>{item.nome_da_fazenda} (#{item.codigo_da_fazenda})</Text>
                        <View style={styles.dadosContent}>
                            <View style={styles.row}>
                                <Text style={{marginRight:'15%'}}>Localização:</Text>
                                <Text style={styles.dado}> {item.latitude}, {item.longitude} </Text>
                            </View>
                            <View style={styles.row}>
                                
                            </View>
                        </View>
                    </View>
                </View>
            </TouchableOpacity>
        </View>
    </>
     
                        
    );
    
  
  return (
        <>
            <View>
                {loading ? <></> : <FlatList
                    data={tipo == "visitar" ? fazendasAvisitar : fazendasVisitadas}
                    renderItem={Item}
                    keyExtractor={item => item?.id_fzd}
                />} 
            </View>
        </>
  );
}
const styles = StyleSheet.create({
    main:{
        flex:1,
        backgroundColor: '#fff',
        alignItems: 'center'
    },
    card:{
        flex:1,
        height: 220,
        width:'95%',
        backgroundColor:'#fff',
        padding: 5,
        elevation:2,
        borderRadius:30,
        padding: 10,
        marginBottom: '5%'
    },
    row:{
        // flex:1,
        flexDirection: 'row',
        justifyContent:'space-around',
        marginVertical:'2%'
    },
    input: {
        height: 40,
        margin: 12,
        padding: 10,
        elevation: 2,
        borderRadius: 30,
        backgroundColor: '#fff',
        color: 'gray',
    },
    button:{
        backgroundColor:'#0076C1',
        borderRadius: 30,
        flex: 1,
        justifyContent: 'center',
    },
    textButton:{
        color: '#fff',
        textAlign: 'center',
        fontWeight:'bold'
    },
    dados:{
        flex:1,
        flexDirection:'column',
        marginLeft: '-10%'
    },
    titulo:{
        textAlign: 'left',
        marginBottom: '6%',
        fontWeight: 'bold',
        fontSize:18
    },
    dado:{
        color: 'gray',
        fontSize:12,
        marginTop: '1%',
        width:'60%'
    },
    icon:{
        marginTop:'20%',
        marginHorizontal: '5%'
    },
    cardDetalhes:{
        height:30,
        borderTopColor:'gray',
        borderTopWidth:2,
        marginTop:'2%',
        flexDirection:'row',
        justifyContent: 'space-around'
    },
    dadosContent:{
        marginTop:'8%',
    }
  });
export default ListaFazendasVisitas;