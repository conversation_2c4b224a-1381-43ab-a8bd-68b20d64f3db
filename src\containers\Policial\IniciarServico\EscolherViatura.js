import React, { useState, useEffect } from 'react';
import { 
    View, 
    Text,
    StyleSheet,
    TextInput,
    Image,
    TouchableOpacity,
} from 'react-native';
import { Icon } from 'react-native-elements';
import axios from 'axios'
import { useNavigation } from '@react-navigation/native';
import { URL_API } from '../../../services/urls'
import viaturaImg from '../../../assets/viatura.png'
import { useUserContext } from '../../../context/AuthContext';
import AsyncStorage from '@react-native-community/async-storage';

function EscolherViatura({ proximo, escolherViatura }) {

    const [viaturas, setViaturas] = useState([])
    const [filterText, setFilterText] = useState('')
    const {user} = useUserContext()
    const [userPm, setUserPm] = useState('')
    const navigation = useNavigation();

    const getUser = async() => {
      const usuario = await AsyncStorage.getItem("@Usuario")
      return setUserPm(JSON.parse(usuario));
    }

    const logout = async () => {
      console.log("ok")
      await AsyncStorage.removeItem('@Id');
      await AsyncStorage.removeItem('@Identificacao');
      await AsyncStorage.removeItem('@Viatura');
      await AsyncStorage.removeItem("@Usuario")
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    };
    
    useEffect(() => {
      const getUser = async () => {
        const usuario = await AsyncStorage.getItem("@Usuario")
        console.log(JSON.parse(usuario))
        if(usuario !== null){
          axios.post(URL_API+'todas_viatura_unidade',{
            id: JSON.parse(usuario)
            //  id_viat: 3
            })
            .then(res=>{
              console.log(res.data)
              setViaturas(res.data)
            })
            .catch(res=>{
              console.log(res)
            })
          }
       }
       
        //axios.get(URL_API+'todas_viaturas').then(res => setViaturas(res.data))
        
      getUser()
    }, [userPm])

  return (

    <View>
        <Text style={{marginBottom: 20}}>Selecione a viatura de serviço</Text>
        <TextInput
            style={styles.input}
            onChangeText={(e) => setFilterText(e)}
            value={filterText}
            placeholder="Digite aqui"
            placeholderTextColor='gray'
        />

        <View style={styles.viaturas}>
        {
            viaturas && 
            viaturas
            .filter(({id_viat = '', placa = '', modelo_viat = ''}) => {
                return (
                    modelo_viat.toLowerCase().includes(filterText.toLowerCase())
                    ||
                    placa.toLowerCase().includes(filterText.toLowerCase())
                    ||
                    String(id_viat).toLowerCase().includes(filterText.toLowerCase())
                )
            })
            .map(viatura => {
            return(
                <TouchableOpacity
                onPress={() => {
                    escolherViatura(viatura)
                    proximo()
                }}
                >
                <View style={styles.viaturaCard}>
                    <View style={{width: 103, height: 62}}>
                    <Image
                        style={styles.viaturaImagem}
                        source={require('../../../assets/viatura.png')}
                        //source={viaturaImg}
                    />
                    </View>
                    <View>
                      <Text style={{fontWeight: 'bold', fontSize: 17, marginBottom: 0}}>Veículo #{viatura.id_viat}</Text>
                      <Text style={{fontSize: 12, marginBottom: 5, color: '#999'}}>Placa: {viatura.placa}</Text>
                      <Text style={{fontSize: 9, color: 'rgba(0, 118, 193, 1)', marginBottom: 5}}>{viatura.modelo_viat}</Text>
                    </View>
                    <View>

                    <Icon
                        name='navigate-next'
                        type='material'
                        color='rgba(0, 118, 193, 1)'
                        style={{alignItems: 'flex-start'}}
                        size={25}
                    />

                    </View>
                </View>
                
                </TouchableOpacity>
            )
            })
        }

        
        </View>
        <TouchableOpacity 
          style={styles.voltar}
          onPress={logout}
        >
          <Text style={{color: 'rgba(0, 118, 193, 1)', textAlign: 'center', fontWeight:'bold'}}>Sair</Text>
        </TouchableOpacity>
    </View>

  );
}

const styles = StyleSheet.create({
    input: {
      height: 50,
      width: '92%',
      borderWidth: 1,
      padding: 15,
      borderColor:'#fff',
      borderRadius: 30,
      elevation:2,
      backgroundColor: '#fff',
      color: '#000',
  },
  viaturas: {
    flex: 1,
  },
  viaturaCard: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    borderWidth: 1,
    borderColor:'#fff',
    backgroundColor: '#fff',
    color: '#000',
    width: '100%',
    marginTop: 15,
    borderRadius: 20,
    elevation: 1,
    height: 97,
  },
  viaturaImagem: {
    width: '100%',
    height: '100%',
  },
  voltar: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 30,
    width: '100%',
    marginTop:'5%',
    borderColor:'rgba(0, 118, 193, 1)',
    borderWidth: 2
  },
})

export default EscolherViatura;