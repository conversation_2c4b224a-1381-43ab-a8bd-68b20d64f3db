import React, { useState, useRef } from 'react';
import { 
    View, 
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import PartidaViatura from './PartidaViatura'
import EscolherViatura from './EscolherViatura'
import ComponentesViatura from './ComponentesViatura'
import axios from 'axios'
import { URL_API } from '../../../services/urls';
import AsyncStorage from '@react-native-community/async-storage';

function IniciarServico() {
  const [fluxo, setFluxo] = useState('initial')
  const [viaturaEscolhida, setViaturaEscolhida] = useState({})
  const [componentesEscolhidos, setComponentesEscolhidos] = useState([])
  const [inputs, setInputs] = useState({
    periodo_data: new Date(),
    periodo_time: new Date(),
    responsavel: "Alguém que falta pegar do banco", //pegar do backend
    quilometragem: "", //Usuário vai colocar
    id_rota:''
  })
  const navigation = useNavigation();

  const handleInputChange = (value, field)=> {
    setInputs(prev => {
      return { ...prev, [field]: value }
    })
  }

  const scrollRef = useRef();

  const scrollTop = () => {
    scrollRef.current.scrollTo({
      y: 0,
      animated: true,
    });
  }
  console.log('vuatraaaa',viaturaEscolhida)

  const submitServico = async () => {
    const dataToSend = {
      ...inputs,
      componentes: componentesEscolhidos,
      viatura: viaturaEscolhida
    }
    await AsyncStorage.setItem('@Viatura', JSON.stringify(viaturaEscolhida.id_viat))
    await AsyncStorage.setItem('@Responsavel',inputs.responsavel)
    await AsyncStorage.setItem('@IdRota', JSON.stringify(inputs.id_rota))
    const data = {
      id_viat: viaturaEscolhida.id_viat,
      componentes_policiais: "'"+dataToSend.componentes+"'",
      kilometragem_inicial: inputs.quilometragem,
      id: inputs.id_rota
    }

    axios.put(URL_API+'salvar_rota3',data)
    .then(res=>{
      console.log('send: ',res.data, data)

      navigation.reset({
        index: 0, 
        routes: [{ name: 'Policial' }],
      });
    })
    .catch(res=>{
      console.log('send: ',dataToSend.componentes)
      console.log(res)
    })
  }
  

  const handleFluxo = () => {
    switch(fluxo) {
      case 'initial':
        return <EscolherViatura 
                  proximo={() => {
                    scrollTop()
                    setFluxo('partida')
                  }}
                  escolherViatura={(viatura) => setViaturaEscolhida(viatura)}
                />
      case 'partida':
          return <PartidaViatura 
                    proximo={() => {
                      scrollTop()
                      setFluxo('componentes')
                    }} 
                    voltar={() =>{
                      scrollTop()
                      setFluxo('initial')
                    }}
                    viaturaEscolhida={viaturaEscolhida}
                    inputs={inputs}
                    setInputs={setInputs}
                    handleInputChange={handleInputChange}
                  />
      case 'componentes':
        return <ComponentesViatura 
                viaturaEscolhida={viaturaEscolhida}
                componentesEscolhidos={componentesEscolhidos}
                setComponentesEscolhidos={(componentes) => setComponentesEscolhidos(componentes)}
                voltar={() =>{
                  scrollTop()
                  setFluxo('initial')
                }}
              />
    }
  }

  return (
      <View style={{flex: 1}}>
        <View style={styles.main}>     

          <ScrollView contentInsetAdjustmentBehavior="automatic" ref={scrollRef}>
            <View style={styles.header}>
                <Text style={{fontWeight: 'bold', color: '#fff', fontSize: 20, marginBottom: 10}}>Inicie o serviço na viatura</Text>
                <Text style={{fontWeight: 'light', color: '#fff', fontSize: 9, marginBottom: 10}}>Para iniciarmos, escolha a viatura do serviço</Text>
            </View>

          <View style={styles.card}>

            {handleFluxo()}
          </View>

          </ScrollView>


        </View>
          {
            fluxo == 'componentes' && 
            <View style={{position: 'absolute', width: '100%', flex: 1, alignItems: 'center', justifyContent: 'center', bottom: 20}}>
              <TouchableOpacity
                onPress={() => submitServico()}
                style={styles.submitButton}
                disabled={componentesEscolhidos < 1}
              >
                <Text style={{color: "#fff", fontSize: 16}}>Iniciar Serviço</Text>
              </TouchableOpacity>
            </View>
          }
      </View>
  );
}


const styles = StyleSheet.create({
  main: {
      flex:1,
      width:'100%',
      alignSelf:"center",
      backgroundColor: 'rgba(0, 118, 193, 1)',
  },
  header: {
      flex: 1,
      alignSelf: 'center',
      alignItems: 'center',
      paddingTop: 40,
      padding: 10,
  },
  card: {
    backgroundColor: '#fff',
    color: '#000',
    paddingBottom:  200,
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  submitButton: {
    width: "80%",
    height: 53,
    backgroundColor: 'rgba(0, 118, 193, 1)',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 100,
  }
})

export default IniciarServico;