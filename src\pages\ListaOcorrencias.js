
 import React,{useState,useEffect} from 'react';
 import {
   SafeAreaView,
   ScrollView,
   StatusBar,
   StyleSheet,
   Text,
   TouchableOpacity,
   View,
 } from 'react-native';
//import MapaFazenda from '../components/MapaFazenda';
import { useNavigation } from '@react-navigation/core';
import axios from 'axios';
import AsyncStorage from '@react-native-community/async-storage';
import { URL_API } from '../services/urls';

 
function ListaOcorrencias() {

  const navigation = useNavigation();
  const [ocorrenciasViatura, setOcorrenciasViatura] = useState([]);
  const [fazendas, setFazendas] = useState([])
  const [proprietatios, setProprietatios] = useState([])


  const fetchOcorrenciasViatura = async () => {

    const id_viat = await AsyncStorage.getItem('@Viatura');
    console.log('id_viat',id_viat)

    const response = await axios.get(URL_API + `ocorrencias_viatura?id_viat=${id_viat}`);
    return response.data;
  }

  const fetchFazendas = async () => {
    const response = await axios.get(URL_API+"todas_fazendas");
    return response.data;
  }

  const fetchProprietarios = async () => {
    const response = await axios.get(URL_API+"selecionar_todos_proprietarios");
    return response.data;
  }

  const joinResult = ( 
    ocorrencias,
    fazendas, 
    proprietarios,
    ) => {
        let newOcorrencias = ocorrencias;
        
        newOcorrencias = newOcorrencias.map((ocorrencia) => {
            const fazenda = fazendas.find((f) => ocorrencia.id_fzd === f.id_fzd)
            const proprietario = proprietarios.find((p) => ocorrencia.id_prop === p.id_prop)
           
            return {
                ...ocorrencia,
                fazendaNome: fazenda ? fazenda.nome_da_fazenda : '',
                fazendaLatitude: fazenda ? fazenda.latitude : '',
                fazendaLongitude: fazenda ? fazenda.longitude : '',
                proprietarioNome: proprietario ? proprietario.nome_do_proprietario : ''
            }
        })
        setOcorrenciasViatura(newOcorrencias);    
  }

  useEffect(()=>{
    Promise.all([
      fetchOcorrenciasViatura(),
      fetchFazendas(),
      fetchProprietarios(),
    ]).then((result)=> {
      const [
        ocorrenciasViatura,
        fazendas,
        proprietarios,
      ] = result;
      //setFazendas(fazendas);
      joinResult(ocorrenciasViatura, fazendas,proprietarios);
    })
  },[])
 
  return (
    <SafeAreaView style={styles.main}>
      <StatusBar />
      <ScrollView>
        <View>
          <Text style={styles.info}>Confira a lista de Ocorrências</Text>
        </View>

          {ocorrenciasViatura.map((item, index) => {
            return (
              <TouchableOpacity onPress={() => navigation.navigate('detalheOcorrencia', {item})} style={styles.card}>
                <View  style={styles.option}>
                  <Text>#{item.id}</Text>
                  <Text>Fazenda: {item.fazendaNome}</Text>
                </View>
              </TouchableOpacity>
            )
          })}

      </ScrollView>
    </SafeAreaView>
  );

}

  const styles = StyleSheet.create({
   
    main:{
      flex:1,
      backgroundColor: '#fff'
    },

    info:{
        marginTop:'5%',
        marginBottom:'5%',
        textAlign: 'center',
        color: 'gray',
        width: '70%',
        alignSelf: 'center'
    },
    card:{
      backgroundColor: '#fff',
      width:'100%',
      height:'auto',
      borderRadius: 30,
      elevation: 1,
      marginBottom: '5%',
    },
    option:{
      backgroundColor:'#fff',
      marginLeft:'5%',
      // marginRight:'0%',
      marginTop:'2%',
      marginBottom:'2%',
      padding:5,
      borderRadius:5,
    },
  });
  
 
 export default ListaOcorrencias;
 