import React, {useState, useEffect} from 'react';
import { View, TouchableOpacity, StyleSheet, Text, Alert, ActivityIndicator } from 'react-native'
import { useUserContext } from '../context/AuthContext'
import { useLocation } from '../context/LocationContext'
import { saveGeolocation } from '../api'
import axios from 'axios'; 
import { useSocketContext } from '../context/socketContext';
import { Dimensions } from 'react-native';
import AsyncStorage, { useAsyncStorage } from '@react-native-community/async-storage';
import {URL_API, URL_SOCKET} from '../services/urls'
import QRCodeScanner from 'react-native-qrcode-scanner';
import { Icon } from 'react-native-elements';
import { useNavigation } from '@react-navigation/native';
import { useRoute } from '@react-navigation/native';
import {useVisitas} from '../context/ContextApi';
import {useNetInfo} from '@react-native-community/netinfo';
import { useFetchValidacaoVisita } from '../context/fetchValidacaoVisita';


const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

function BotaoQr() {

  const {handleValidar} = useFetchValidacaoVisita();
  const route = useRoute();

  const { fazendas } = route.params;
  const { location } = useLocation();
  const [fluxo, setFluxo] = useState('qr');
  const [oil, setOil] = useState(false);
  const [pernoite, setPernoite] = useState(false);
  const [idFazenda, setIdFazenda] = useState()
  const [idRota, setIdRota] = useState();
  const [idViatura, setIdViatura] = useState();


  const handleEnviarQrCode = (e) =>{
    // fazendas.map(data=>{
    //   if(data.id_fzd.includes(e.data)){
        setFluxo('valida')
        setIdFazenda(Number(e.data));
    //   }
    // })
    console.log('data', e.data)
  }

  useEffect(()=>{
    async function getIDs(){
      const idViat = await AsyncStorage.getItem('@Viatura')
      setIdViatura(Number(idViat))

      const idRot = await AsyncStorage.getItem('@IdRota')
      setIdRota(Number(idRot))
    }
    getIDs();
  },[]);

  const dataValidacao = {
    latitude: location.latitude,
    longitude: location.longitude, 
    status_visita: 'concluida',
    id_viatura: idViatura, 
    fzd_id: idFazenda,
    id_rota: idRota,
    pernoite:pernoite?1:0,
    combustivel: oil?1:0,
  }

  return (
    <View style={styles.main}>
      {fluxo == 'qr'
        ?
        <QRCodeScanner
          onRead={e=>{handleEnviarQrCode(e)}}
          showMarker={true}
          checkAndroid6Permissions={true}
          topContent={
              <View style={styles.title}>
                  <Text styles={styles.titleText}>Para concluir a visita aponte o celular para o QR Code existente na fazenda.</Text>
              </View>
          }
        />
        :
        <>
          <View style={styles.titleExtras}>
            <Text style={styles.titleExtrasText}>Selecione os extras da fazenda</Text>
          </View>
          <View style={styles.extras}>
            <TouchableOpacity style={styles.touchable} onPress={()=>setOil(!oil)}>
              <Icon
                  name='local-gas-station'
                  type='material'
                  color={oil ? '#3F98D0' : 'gray'}
                  //style={styles.icon}
                  size={95}
              />
              <Text style={styles.touchableText}>Combustível</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.touchable} onPress={()=>setPernoite(!pernoite)}>
              <Icon
                  name='moon'
                  type='feather'
                  color={pernoite ? '#3F98D0' : 'gray'}
                  //style={styles.icon}
                  size={95}
              />
              <Text style={styles.touchableText}>Pernoite</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity style={styles.buttonValidar} onPress={() => handleValidar(dataValidacao)}>
            <Text style={styles.textButton}>Validar visita</Text>
          </TouchableOpacity>
        </>
      }
      
    </View>
  );
}

const styles = StyleSheet.create({
  main:{
    flex: 1,
  },
  title:{
    marginTop:'-15%',
    width:'80%',
    alignItems:"center"
  },
  titleExtras:{
    marginTop:"10%",
    alignSelf:"center"
  },
  titleExtrasText:{
    fontSize:18
  },
  extras:{
    flex:1,
    marginTop: '50%',
    flexDirection:"row",
    justifyContent:'space-around',
    // marginHorizontal:'10%'
  },
  touchable:{
    alignItems:"center"
  },
  touchableText:{
    fontSize:17,
    fontWeight:'bold'
  },
  buttonValidar:{
    backgroundColor:'rgba(0, 118, 193, 1)',
    alignItems:'center',
    width:'100%',
    alignSelf: 'center',
    padding: '4%',
    position:'relative',
  },
  textButton:{
      color:'#fff',
      fontSize:17
  }
});

export default BotaoQr;