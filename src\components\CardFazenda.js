import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ScrollView
} from 'react-native';
import MapboxGL from "@react-native-mapbox-gl/maps";
import { Dimensions } from 'react-native'
import { useLocation } from '../context/LocationContext';
import { useAlertContext } from '../context/AlertDadosContext';
import * as Progress from 'react-native-progress';
import { Icon } from 'react-native-elements'
import { Button } from 'react-native-elements/dist/buttons/Button';
import BotaoEnviarViatura from './BotaoEnviarViatura';
import BotaoSelectViatura from './BotaoSelectViatura';
import { useNavigation } from '@react-navigation/core';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

function CardFazenda({name, p, sh, item }) {

  const navigation = useNavigation()
  console.log('item', item)
  return (
    <>
      <View style={[styles.main, { backgroundColor: p == 0 ? "rgba(0, 118, 193, 1)" : null }]}>
      </View>
      <View style={styles.mainCard}>
        <View style={styles.card}>
          <View style={styles.texts}>
            <View>
              <Text style={styles.nome}>Nome da Fazenda</Text>
              <Text style={styles.nomeContent}>{item ? item.nome_da_fazenda : name}</Text>
            </View>
            {/* <View>
              <Text style={styles.nome}>Existe uma visita programada para</Text>
              <Text style={styles.nomeContent}>23/05/2021</Text>
            </View> */}
          </View>

          {!sh? <View>
            <TouchableOpacity styles={styles.detalhe} onPress={() => navigation.navigate('Details',{
                nome_da_fazenda:item.nome_da_fazenda, 
                idFazenda:item.id_fzd,
                idArea: item.id_area
              })}>
              <Text style={styles.detalheText}>Detalhes da Fazenda</Text>
              <Icon
                name='arrow-forward'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={25}
              />
            </TouchableOpacity>
          </View>
            : null
          }
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  main: {
    padding: 15,
    height: 100,
    borderBottomLeftRadius: 50,
    borderBottomRightRadius: 50,
    flex: 1,
  },
  mainCard: {
    paddingTop: 10,
  },
  card: {
    backgroundColor: '#fff',
    width: '89%',
    flex: 1,
    alignSelf: 'center',
    elevation: 1,
    borderRadius: 30,
    marginTop: '-25%',
    padding: 15,
  },
  texts: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-around'
  },
  nome: {
    color: 'gray'
  },
  nomeContent: {
    fontSize: 17
  },
  detalhe: {
    flex: 1,
  },
  detalheText: {
    color: 'rgba(0, 118, 193, 1)',
    alignSelf: 'center',
    marginTop: '2%'
  }
});

export default CardFazenda;