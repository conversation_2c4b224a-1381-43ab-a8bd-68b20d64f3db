import React from 'react'
import { View, StatusBar, Button, ScrollView, StyleSheet, TouchableOpacity, Text } from 'react-native'
import CardFazenda from '../../components/CardFazenda'
import Gerentes from './Gerentes'
import Header from '../../components/Header';
import BotaoListagem from '../../components/BotoesListagem';
import AsyncStorage from '@react-native-community/async-storage';
import QrFazenda from '../../components/QrFazenda';

function DetailPage({route, navigation}) {

    const logout = async () => {
        await AsyncStorage.removeItem('@Id');
        await AsyncStorage.removeItem('@Identificacao');
        navigation.reset({
          index: 0,
          routes: [{ name: 'Login' }],
        });
      };

    const name = route.params.nome_da_fazenda;
    const id = route.params.idFazenda
    const idArea = route.params.idArea
    return (
        <View>       
            <StatusBar barStyle={'light-content'} />
            <ScrollView contentInsetAdjustmentBehavior="automatic">
            <Header subtitle="Proprietário" />

                <CardFazenda sh="1" p="0" name={name}/>
                <View style={{ width: '100%'}}>
                  <BotaoListagem id={'FZD'} nomeFZD={id} nomeAreaFZD={idArea}/>
                </View>
            <QrFazenda id={id}/>
              <TouchableOpacity
                style={styles.voltar}
                onPress={() => {
                  navigation.goBack()
                }}
              >
                <Text style={{color: 'rgba(0, 118, 193, 1)', textAlign: 'center', fontWeight:'bold'}}>Voltar</Text>
              </TouchableOpacity>
            </ScrollView>
            {/* <Gerentes /> */}
            {/* <Button onPress={logout} title="Sair" /> */}
            {/* {location &&
              <Text>Localização atual: {location.latitude}, {location.longitude}</Text>
            } */}
        </View>
    )
}

const styles = StyleSheet.create({
  voltar: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 30,
    width: '100%',
    marginTop:'5%',
    borderColor:'rgba(0, 118, 193, 1)',
    borderWidth: 2,
    marginBottom:5
  },
})

export default DetailPage
