import React, {useState, useContext, useRef, useEffect} from 'react';
import {AppState, StyleSheet} from 'react-native';
import {
  TouchableOpacity,
  Animated,
  View,
  Text,
  StatusBar,
  ScrollView,
  TextInput,
  VirtualizedList,
} from 'react-native';

import { TabView, SceneMap } from 'react-native-tab-view';

import {useNavigation} from '@react-navigation/native';
import MapaFazendas from '../components/MapaFazendas';
import axios from 'axios';
import {Dimensions} from 'react-native';
import ListRoutes from '../components/ListRoutes';
import {URL_API} from '../services/urls';
const window = Dimensions.get('window');
const width = window.width;
const height = window.height;
import { useWindowDimensions } from 'react-native';


function Route() {  

  const [Police, setPolice] = useState("")
  
  const _renderTabBar = (props) => {

    const inputRange = props.navigationState.routes.map((x, i) => i);

    return (
      <View style={styles.tabBar}>
        {props.navigationState.routes.map((route, i) => {
          const opacity = props.position.interpolate({
            inputRange,
            outputRange: inputRange.map((inputIndex) =>
              inputIndex === i ? 1 : 0.5
            ),
          });

          return (
            <TouchableOpacity
              style={styles.tabItem}
              onPress={() => setIndex( i )}>
              <Animated.Text style={{ opacity }}>{route.title}</Animated.Text>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  const layout=useWindowDimensions()

 
  const [with1, setwith] = useState("")
  const [withO, setwithO] = useState("")

  const FirstRoute = () => (
    <ListRoutes data={with1} Police={Police}/>
  );
  
  const SecondRoute = () => (
    <ListRoutes data={withO}/>
  );
  
  const renderScene = SceneMap({
    first: FirstRoute,
    second: SecondRoute,
  });

  const [index, setIndex] = React.useState(0);
  const [routes] = React.useState([
    { key: 'first', title: 'With Vehicals' },
    { key: 'second', title: 'With Out Vehicals' },
  ]);

  const [data, setData] = useState();
  // const navigation = useNavigation();
  useEffect(
    () =>
      axios.get(URL_API + 'todas_rotas').then(res => setData(res.data)),
    [],
  );
  useEffect(
    () =>
      axios.get(URL_API + 'todos_policiais').then(res => setPolice(res.data)),
    []
  );

  useEffect(() => {
    if(data){
      setwith(()=>data.filter(ele=>ele.id_viat))
      setwithO(()=>data.filter(ele=>!ele.id_viat))
    }

  }, [data])

  

  return (
    <View style={styles.main}>
      <StatusBar barStyle={'light-content'} />
      <View style={styles.blueCard}>
          <View>
            <Text style={styles.title}>Rotas existentes</Text>
          </View>
      </View>
      <View style={{height:'100%',backgroundColor:'white',borderTopRightRadius:40,borderTopLeftRadius:40}}>
        <View style={{flexDirection:'row',marginTop:30,borderTopRightRadius:20,marginBottom:-40,height:20,padding:0,margin:0,justifyContent:'space-evenly'}}>
          <Text style={{marginRight:"30%",fontSize:15}}>{with1.length}</Text>
          <Text style={{fontSize:15}}>{withO.length}</Text>
        </View>

        <TabView
          renderTabBar={_renderTabBar}
          navigationState={{ index, routes }}
          renderScene={renderScene}
          onIndexChange={setIndex}
          initialLayout={{ width: layout.width }}
        />
      </View>
    </View>
  );
}
const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    paddingTop: 30,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
  },
  main: {
    height:"100%",
    backgroundColor: 'rgba(0, 118, 193, 1)',
  },
  blueCard: {
    marginTop:-30,
    width: '100%',
    height: 100,
    backgroundColor: 'rgba(0, 118, 193, 1)',
    padding: 5,
  },
  title: {
    lineHeight: 90,
    color: '#fff',
    textAlign: 'left',
    marginLeft: '5%',
    fontSize: 20,
    fontWeight: 'bold',
  },

  input: {
    height: 40,
    margin: 12,
    padding: 10,
    elevation: 2,
    borderRadius: 30,
    backgroundColor: '#fff',
    color: 'gray',
  },
});
export default Route;
