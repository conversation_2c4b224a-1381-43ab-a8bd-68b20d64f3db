import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, Text, Alert, ActivityIndicator, Modal, TextInput } from 'react-native'

import axios from 'axios';
import { useNavigation } from '@react-navigation/core';
import { Dimensions } from 'react-native';
import { URL_API, URL_SOCKET } from '../services/urls'
import { Icon } from 'react-native-elements';
import AsyncStorage from '@react-native-community/async-storage';
import {useVisitas} from '../context/ContextApi';
import { useFetchValidacaoVisita } from '../context/fetchValidacaoVisita';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

function BotaoEncerrarRota() {

  const {rota, setRota} = useVisitas();

  const navigation = useNavigation();
  const [dataValidacao, setDataValidacao] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [justificativa, setJustificativa] = useState();
  const [fazendas, setFazendas] = useState([]);
  const [idRota, setIdRota] = useState(null);

  const {disable} = useFetchValidacaoVisita();

  useEffect(()=>{
    async function getRota(){
      const id = await AsyncStorage.getItem('@IdRota') 
      setIdRota(Number(id))  
    }
    getRota()
    
  }, [])

  useEffect(() => {
    getFazendasRotas(true);
  }, [rota])

  
  const getFazendasRotas = async () => {
    
      const id = await AsyncStorage.getItem('@IdRota')
      axios.post(URL_API+'buscar_rota', {id: Number(id)})
      .then(res=>{
        setRota(res.data)
        const lista_fazendas = res.data.fazendas;
        const fazendas_visitadas = res.data.fazendas_visitadas

        const fazendas = lista_fazendas.filter((fazenda) => {
        return !fazendas_visitadas.some((fazenda_visitada) => {
          return fazenda.id_fzd == fazenda_visitada.fzd_id
      })
    })
      setFazendas(fazendas)
 
    })
      .catch(res=>console.log('errorVisitas',res))
  }

  const getIsFinished = (decidir) =>{

    const lista_fazendas = rota.fazendas || [];
    const fazendas_visitadas = rota.fazendas_visitadas || [];

    const fazendas = lista_fazendas.filter((fazenda) =>{
      return !fazendas_visitadas.some((fazenda_visitada) => {
        return fazenda.id_fzd == fazenda_visitada.fzd_id
      })
    })
    //setFazendas(fazendas)
    if(fazendas.length == 0){
      return true
    }
    if(fazendas.length > 0 && !decidir){
      return false
      //Alert.alert('','Termine todas as visitas para encerrar o serviço')
    }

  }

  const handleEncerrarPress = () =>{
    navigation.navigate('encerrarRota',{
      id: idRota,
      justificativa:justificativa // Enviada pelo Modal
    })
  }

  return (
        <>    
          {getIsFinished()
            ? 
            <TouchableOpacity style={[styles.panicButton, disable && styles.botaoDisabled]} onPress={()=>handleEncerrarPress()} disabled={disable}>
              <Icon
                name='timer'
                type='material'
                color='#3F98D0'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButton}>Encerrar Serviço</Text>
            </TouchableOpacity>
            :
            <TouchableOpacity style={[styles.panicButtonDisabled, disable && styles.botaoDisabled]} onPress={() => {getFazendasRotas(false); setModalVisible(true)}} disabled={disable}>
              <Icon
                name='timer'
                type='material'
                color='#fff'
                style={styles.icon}
                size={45}
              />
              <Text style={styles.textButtonDisabled}>Encerrar Serviço Antecipadamente</Text>
            </TouchableOpacity>
          }
            <Modal 
              visible={modalVisible}
              animationType="fade"
              transparent={true}
              onRequestClose={()=> setModalVisible(false)}
            >
              <View style={styles.box}>
                <View style={styles.boxBody}>
                  <View>
                    <Text style={styles.info}>Escreva sua Justificativa</Text>
                  </View>

                  <View style={styles.cardModal}>  
                    <TextInput
                      style={styles.input}
                      onChangeText={(e) => setJustificativa(e)}
                      value={justificativa}
                      placeholder="Digite aqui"
                      placeholderTextColor='gray'
                      maxLength={255}
                      numberOfLines={6}
                      multiline
                    />
                  </View>

                    <TouchableOpacity  style={styles.voltar}>
                      <Text style={styles.textVoltar} onPress={() => {
                        setModalVisible(false)
                        navigation.navigate('Policial')
                      }} >Voltar</Text>
                    </TouchableOpacity>

                    <TouchableOpacity 
                      style={styles.encerrarRota}
                      onPress={() => {setModalVisible(false); handleEncerrarPress()}}>
                      <Text style={styles.textEncerrarRota}>Confirmar</Text>
                    </TouchableOpacity>
                  </View>
                </View>
            </Modal>
        </>
  );
}

const styles = StyleSheet.create({
  main: {
    flex: 1,
    alignItems: 'center',
  },
  row: {
    flex: 1,
    flexDirection: 'row',
    marginBottom:'20%'
  },
  panicButton: {
    padding: 15,
    borderRadius: 30,
    marginTop: '2%',
    width: '47%',
    backgroundColor: '#fff',
    elevation: 1
  },
  textButton: {
    color: '#000',
    fontSize: 15,
    textAlign: 'left',
  },
  icon: {
    alignItems: 'flex-start',
  },
  panicButtonDisabled:{
    padding: 15,
    borderRadius: 30,
    marginTop: '2%',
    width: '45%',
    backgroundColor: 'gray',
    elevation: 1,
    marginHorizontal: '2%'
  },
  textButtonDisabled:{
    color: '#fff',
    fontSize: 15,
    textAlign: 'left',
  },
  //Conteudo do Modal
  box:{
    width:'100%',
    height:'100%',
    backgroundColor:'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  boxBody:{
    width:'80%',
    height:'80%',
    backgroundColor:'#fff',
    borderRadius: 30,
  },
  input: {
    width: '92%',
    borderWidth: 1,
    padding: 10,
    borderColor: '#fff',
    borderRadius: 30,
    elevation: 2,
    backgroundColor: '#fff',
    color: '#000',
    alignSelf:'center',
  },
  info:{
    marginTop:'5%',
    marginBottom:'5%',
    textAlign: 'center',
    color: 'gray',
    width: '70%',
    alignSelf: 'center'
  },
  cardModal:{
    backgroundColor: '#fff',
    width: '100%',
    height:'auto',
    borderRadius: 30,
    elevation: 0,
    marginBottom: '10%',
  },
  voltar: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 30,
    width: '90%',
    borderColor:'rgba(0, 118, 193, 1)',
    borderWidth:2,
    marginBottom: '10%',
    alignSelf: 'center'
  },
  textVoltar:{
    color:'rgba(0, 118, 193, 1)', 
    textAlign: 'center', 
    fontWeight:'bold',
  },
  encerrarRota:{
    backgroundColor: 'rgba(0, 118, 193, 1)',
    padding: 20,
    borderRadius: 30,
    width: '90%',
    borderColor:'rgba(0, 118, 193, 1)',
    borderWidth:2,
    alignSelf: 'center'
  },
  textEncerrarRota:{
    color:'#fff', 
    textAlign: 'center', 
    fontWeight:'bold',
  },
  botaoDisabled: {
    opacity: 0.5
  }
  
});

export default BotaoEncerrarRota;