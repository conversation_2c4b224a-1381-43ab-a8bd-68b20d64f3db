import React, { useState, useContext, useRef, useEffect } from 'react';
import { AppState, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { View, Text, StatusBar, ScrollView } from 'react-native';
import { useLocation } from '../../context/LocationContext';
import { useUserContext } from '../../context/AuthContext';
import { usePermission } from '../../context/LocationPermissionContext';
import ReactNativeForegroundService from '@supersami/rn-foreground-service';
import { Button } from 'react-native-elements';

import Header from '../../components/Header';
import Mapa from '../../components/Mapa';
import AsyncStorage from '@react-native-community/async-storage';
import { Dimensions } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import BotaoPanico from '../../components/BotaoPanico';
import BotaoQr from '../../components/BotaoQr';
import AlertaViatura from '../../components/AlertaViatura';
import BotaoListagem from '../../components/BotoesListagem'
import AlertaPanico from './AlertaPanico';
import MapaRotaViatura from '../../components/MapaRotaViatura';
import axios from 'axios';
import { useSocketContext } from '../../context/socketContext';
import {useNetInfo} from '@react-native-community/netinfo';
import { useFetchValidacaoVisita } from '../../context/fetchValidacaoVisita';


const window = Dimensions.get('window');

const width = window.width;

function Policial() {
  // const [toggle, settoggle] = useState(0)

  // useEffect(() => {
  //   setInterval(() => {
  //     settoggle(toggle=>toggle+1)
  //   }, 5000);
  // }, [])

  // const handleNotification = () => {
  //   PushNotification.localNotification(
  //     {
  //       channelId: 'testing',
  //       title: 'Hello buggar your are in login',
  //       bigText: 'This is the extracted text from rthe rth index',
  //       message: "HolyFudge"
  //     }
  //   )
  // }

  // useEffect(() => {
  //   if(toggle){
  //     handleNotification()
  //   }
  // }, [toggle])

  const navigation = useNavigation();
  const { location } = useLocation();
  const { requestLocationPermission, findCoordinates } = usePermission();
  const appState = useRef(AppState.currentState);
  const [appStateVisible, setAppStateVisible] = useState(appState.current);
  const [nome, setNome] = useState();
  const [logged, setLogged] = useState();
  const netInfo = useNetInfo();
  
  
  const {socket} = useSocketContext();


  useEffect(() => {
    AppState.addEventListener('change', _handleAppStateChange);
    return () => {
      AppState.removeEventListener('change', _handleAppStateChange);
    };
  }, []);

  useEffect(()=>{
    socket.on('connect',(data)=>{
      console.log('conecatar', data)
    })
  },[socket])

  const _handleAppStateChange = nextAppState => {
    appState.current = nextAppState;
    setAppStateVisible(appState.current);
    //console.log("AppState", appState.current);

    if (appState.current == 'inactive' || appState.current == 'background') {
      console.log('if', appState.current);
      ReactNativeForegroundService.add_task(
        () => {
          findCoordinates();
        },
        {
          delay: 1000,
          onLoop: false,
          taskId: 'taskid',
          onError: e => console.log('Error logging:', e),
        },
      );
      ReactNativeForegroundService.start({
        id: 144,
        title: 'AIBA',
        message: 'O aplicativo está rodando em segundo plano!',
      });
    } else {
      console.log('else', appState.current);
      ReactNativeForegroundService.stop();
      ReactNativeForegroundService.remove_all_tasks();
    }
  };

  const logout = async () => {
    await AsyncStorage.removeItem('@Id');
    await AsyncStorage.removeItem('@Identificacao');
    await AsyncStorage.removeItem('@Viatura');
    await AsyncStorage.removeItem("@Nome");
    await AsyncStorage.removeItem("@Usuario");
    setLogged(false);
    ReactNativeForegroundService.stop();
    ReactNativeForegroundService.remove_all_tasks();
    navigation.reset({
      index: 0,
      routes: [{ name: 'Login' }],
    });
  };

  useEffect(() => {
    (async () => {
      await requestLocationPermission();
      await findCoordinates();
    })();
  }, []);

  return (
    <View>
      <StatusBar barStyle={'light-content'} />
      <ScrollView contentInsetAdjustmentBehavior="automatic">
        <Header subtitle="Policial" />
        <AlertaPanico />
        {/* <AlertaViatura/>
        <BotaoPanico /> */}
        {/* <TouchableOpacity
          onPress={() => navigation.navigate('CadViatura')}
          style={styles.panicButton}>
          <Text style={styles.textButton}>CadViatura</Text>
        </TouchableOpacity> */}
        <BotaoListagem id={'PM'} />
        {/* <MapaRotaViatura /> */}
        {/* <Button
          onPress={() => navigation.navigate('IniciarServico')}
          title="Iniciar Serviço"></Button> */}
        {/* <Button onPress={logout} title="Sair" /> */}
        <TouchableOpacity 
          style={styles.voltar}
          onPress={logout}
        >
          <Text style={{color: 'rgba(0, 118, 193, 1)', textAlign: 'center', fontWeight:'bold'}}>Sair</Text>
        </TouchableOpacity>
        {/* {location &&
            <Text>Localização atual: {location.latitude}, {location.longitude}</Text>
          } */}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  panicButton: {
    padding: 15,
    backgroundColor: '#b94343',
    borderRadius: 30,
    marginTop: 15,
    width: width - 50,
    alignItems: 'center',
  },
  textButton: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  voltar: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 30,
    width: '100%',
    marginTop:'5%',
    borderColor:'rgba(0, 118, 193, 1)',
    borderWidth: 2
  },
});

export default Policial;
