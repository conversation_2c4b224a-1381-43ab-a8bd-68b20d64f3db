import React, { useState, useContext, useRef, useEffect } from 'react';
import { AppState, StyleSheet, TouchableOpacity } from "react-native";
import {
  View,
  Text,
  StatusBar,
  ScrollView,
  TextInput,
  VirtualizedList,
  FlatList
} from 'react-native';

import { useNavigation } from '@react-navigation/native';

import { Icon } from 'react-native-elements';

import { Dimensions } from 'react-native'
import axios from 'axios';
import { URL_API } from '../services/urls';
import Gerentes from '../containers/Fazendeiro/Gerentes';
import MapaDetalhe from '../components/MapaDetalheFazenda';

import Geocode from "react-geocode";

Geocode.setApiKey("AIzaSyBzs8PRhcp0Wgu3m90fu_tyV5KmhYBzJbQ");

Geocode.setLanguage("pt-br");

Geocode.setRegion("br");

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;


function Informacoes(props) {

  const navigation = useNavigation();

//   useEffect(()=>{
//     getEndereco();
//   })

//   const getEndereco = async () =>{
//     Geocode.fromLatLng(props.data.latitude, props.data.latitude).then(
//     (response) => {
//       const address = response.results[0].formatted_address;
//       console.log(address);
//     },
//     (error) => {
//       console.error(error);
//     }
//   );}
  return (
    <View>
        <View>
            <MapaDetalhe latitude={props.data.latitude} longitude={props.data.longitude}/>
            <View style={styles.cardLoc}>
                <Text style={styles.titulo}>Localização atual:</Text>
                <Text style={styles.endereco}>Avenida Rio Branco, KM 32 - 12345-234</Text>
                <View style={styles.div}>
                </View>
                <View style={styles.cardContentLoc}>
                    <View style={styles.cardHeaderLoc}>
                        <Text style={styles.cardTitulo}>Apelido:</Text>
                        <Text style={styles.cardContentTitulo}> {props.data.nome_da_fazenda}</Text>
                    </View>
                    <View style={styles.cardHeaderLoc}>
                        <Text style={styles.cardTitulo}>Cadastrada em:</Text>
                        <Text style={styles.cardContentTitulo}> {props.data.data_do_cadastro} - {props.data.hora_do_cadastro}</Text>
                    </View>
                </View>
            </View>
        </View>
        <View>
            <Gerentes/>
        </View>
        <View style={styles.dados}>
              <Text style={styles.titleDados}>Dados da Fazenda</Text>
              <View style={styles.cardDados}>
                  <Text style={styles.titleInfo}>Código da fazenda</Text>
                  <Text>{props.data.codigo_da_fazenda}</Text>
                  <View style={styles.div}>
                  </View>
                  <Text style={styles.titleInfo}>Nome da fazenda</Text>
                  <Text>{props.data.nome_da_fazenda}</Text>
                  <View style={styles.div}>
                  </View>
                  <Text style={styles.titleInfo}>Proprietário</Text>
                  <Text>{props.data.proprietario}</Text>
                  <View style={styles.div}>
                  </View>
                  <Text style={styles.titleInfo}>Área</Text>
                  <Text>{props.data.area_de_operacao}</Text>
                  <View style={styles.div}>
                  </View>
                  <Text style={styles.titleInfo}>Latitude</Text>
                  <Text>{props.data.latitude}</Text>
                  <View style={styles.div}>
                  </View>
                  <Text style={styles.titleInfo}>Longitude</Text>
                  <Text>{props.data.longitude}</Text>
                  <View style={styles.div}>
                  </View>
                  <Text style={styles.titleInfo}>Bairro</Text>
                  <Text>123456</Text>
                  <View style={styles.div}>
                  </View>
                  <Text style={styles.titleInfo}>Município</Text>
                  <Text>123456</Text>
                  <View style={styles.div}>
                  </View>
              </View>
        </View>
    </View>
  );
}
const styles = StyleSheet.create({
    
    div:{
        borderBottomColor:'#E4E9EB',
        borderBottomWidth:2,
        width:"95%",
        height:2,
        alignSelf:'center',
        marginTop:'5%',
        marginBottom:"5%"
    },
    cardLoc:{
        backgroundColor:'#fff',
        width:'87%',
        alignSelf:'center',
        padding:15,
        elevation:1,
        borderRadius:30,
        marginTop:'-20%'
    },
    cardHeaderLoc:{
        flexDirection:'row'
    },
    titulo:{
        fontWeight:'bold'
    },
    endereco:{
        color: '#3F98D0',
        fontSize:13,
        fontWeight:'bold'
    },
    cardTitulo:{
        fontWeight:'bold'
    },
    cardContentTitulo:{
        fontSize:13,
        color:'gray'
    },
    dados:{
        marginTop:'5%'
    },
    titleDados:{
        fontSize:17,
        textAlign:'center'
    },
    cardDados:{
        backgroundColor:'#fff',
        width:'90%',
        alignSelf:'center',
        borderRadius:20,
        borderColor:'#E4E9EB',
        borderWidth:1,
        marginTop:'2%',
        padding:15
    },
    titleInfo:{
        color: 'gray',
    }
  });
export default Informacoes;