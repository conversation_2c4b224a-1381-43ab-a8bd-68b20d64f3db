import React, { useEffect, useState } from 'react';
import { 
    View, 
    Text,
    StyleSheet,
    TextInput,
    Image,
    TouchableOpacity,
} from 'react-native';

import DateTimePicker from '@react-native-community/datetimepicker';
import axios from 'axios';
import { useNavigation } from '@react-navigation/core';
import { URL_API } from '../../../services/urls';
import AsyncStorage from '@react-native-community/async-storage';
import * as Progress from 'react-native-progress';

function PartidaViatura({ viaturaEscolhida, handleInputChange, inputs, proximo, voltar, setInputs }) {
  const navigation = useNavigation()
  const [showDate, setShowDate] = useState(false);
  const [showTime, setShowTime] = useState(false);
  const [semRota, setSemRota] = useState(false);
  const [loading, setLoading] = useState(false)

  async function setId(){
    await AsyncStorage.setItem('@Viatura', JSON.stringify(viaturaEscolhida.id_viat))
  }

  async function setIdRota(dataRota, dataRes){
    await AsyncStorage.setItem('@IdRota', JSON.stringify(dataRota))
    await AsyncStorage.setItem('@Responsavel', dataRes)
  }


  useEffect(()=>{
        setLoading(true)
        axios.post(URL_API+'detalhe_responsavel',{
          id: viaturaEscolhida.id_viat
        })
        .then(res=>{
          console.log(res.data)
          const data = res.data.data_inicio.split('-')
          const dataBr = data[2]+'-'+data[1]+'-'+data[0]
          setInputs({
            id_rota: res.data.id,
            periodo_data: dataBr,
            periodo_time: res.data.hora_requisicao,
            responsavel: res.data.nome, //pegar do backend
            quilometragem: "", //Usuário vai colocar
          })
          setIdRota(res.data.id, res.data.nome)
          axios.post(URL_API+'buscar_rota',{
            id: res.data.id
          })
          .then(res=>{
            if(res.data.rota.status == 'iniciada'){
              setId()
              navigation.navigate('Policial')
            }
            setLoading(false)
          })
        })
        .catch(res=>{
          console.log(res)
          if(res){
            setLoading(false)
            setSemRota(true)
          }
    })
    
  },[])

  const showDatepicker = () => {
    setShowDate(true);
  };

  const showTimepicker = () => {
    setShowTime(true);
  };

  const formatTime = date => {
    const hours = date.getHours() > 9 ? date.getHours() : "0" + date.getHours() 
    const minutes = date.getMinutes() > 9 ? date.getMinutes() : "0" + date.getMinutes() 
    return hours + ":" + minutes
  }

  // const formatDate = date => {
  //   const day = date.getDate() > 9 ? date.getDate() : "0" + date.getDate() 
  //   const month = date.getMonth() + 1 > 9 ? date.getMonth() + 1 : "0" + (date.getMonth() + 1)
  //   const year = date.getFullYear() > 9 ? date.getFullYear() : "0" + date.getFullYear() 
  //   return day + "/" + month + "/" + year
  // }

  return (
    <>
      {!loading
        ?
        <View>
          <View style={styles.viaturaCard}>
            <View style={{width: 103, height: 62}}>
            <Image
                style={styles.viaturaImagem}
                source={require('../../../assets/viatura.png')}
            />
            </View>
            <View>
              <Text style={{fontWeight: 'bold', fontSize: 17, marginBottom: 0}}>Veículo #{viaturaEscolhida.id_viat}</Text>
              <Text style={{fontSize: 12, marginBottom: 5, color: '#999'}}>Placa: {viaturaEscolhida.placa}</Text>
              <Text style={{fontSize: 9, color: 'rgba(0, 118, 193, 1)', marginBottom: 5}}>{viaturaEscolhida.modelo_viat}</Text>
            </View>
            <View>

            </View>
        </View>
        {semRota 
          ?
          <>
            <Text>Viatura sem Rota Definida</Text>
            <View style={{width: '100%', flex: 1, alignItems: 'center', marginTop: 30,}}>
              <TouchableOpacity 
                style={styles.prosseguir}
                onPress={() => {
                  voltar()
                }}
              >
                <Text style={{color: '#fff', textAlign: 'center'}}>Voltar</Text>
              </TouchableOpacity>
            </View>
            </>
          :
          <>
             <View>
              <Text style={styles.inputLabel}>Responsável pela viatura</Text>
              <TextInput
                style={styles.input}
                //onChangeText={(v) => handleInputChange(v, 'responsavel')}
                value={inputs.responsavel}
                placeholder="Digite aqui"
                placeholderTextColor='gray'
                name="responsavel"
                editable={false}
              />
            </View>
            <View>
              <Text style={styles.inputLabel}>Período do serviço</Text>
              <View style={{flex: 1, flexDirection: "row", alignItems: 'center'}}>
                <View>
                  <TouchableOpacity
                    onPress={showDatepicker}
                  >
                    <TextInput
                      style={{...styles.input, width: '100%', marginRight: 20,}}
                      value={inputs.periodo_data}
                      editable={false}
                    />
                  </TouchableOpacity>
                {
                  /* showDate &&
                  <DateTimePicker
                      style={{...styles.input, width: '100%', marginRight: 20,}}
                      value={inputs.periodo_data}
                      mode={'date'}
                      is24Hour={true}
                      display="default"
                      name="periodo_data"
                      onChange={(e, selected) => {
                        selected && handleInputChange(selected, 'periodo_data')
                        setShowDate(false)
                      }}
                    /> */
                }
                </View>

                <View>
                  <TouchableOpacity
                    onPress={showTimepicker}
                  >
                    <TextInput
                      style={{...styles.input, width: '100%', marginLeft: 30,}}
                      value={inputs.periodo_time}
                      editable={false}
                    />
                  </TouchableOpacity>
                  {
                    /* showTime && 
                    <DateTimePicker
                      style={{...styles.input, width: '100%', marginRight: 20,}}
                      value={inputs.periodo_time}
                      mode={'time'}
                      is24Hour={true}
                      display="default"
                      name="periodo_time"
                      onChange={(e, selected) => {
                        selected && handleInputChange(selected, 'periodo_time')
                        setShowTime(false)
                      }}
                    /> */
                  }
                </View>
              </View>
            </View>
            <View>
              <Text style={styles.inputLabel}>Qual a quilometragem atual da viatura?</Text>
              <TextInput
                style={styles.input}
                onChangeText={(v) => handleInputChange(v, 'quilometragem')}
                value={inputs.quilometragem}
                placeholder="Digite aqui"
                placeholderTextColor='gray'
                name="quilometragem"
                keyboardType="numeric"
              />
            </View>

            <View style={{width: '100%', flex: 1, alignItems: 'center', marginTop: 30,}}>
              <TouchableOpacity 
                style={styles.prosseguir}
                onPress={() => {
                  proximo()
                }}
                disabled={inputs.quilometragem == ''}
              >
                <Text style={{color: '#fff', textAlign: 'center'}}>Prosseguir</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.voltar}
                onPress={() => {
                  voltar()
                }}
              >
                <Text style={{color: 'rgba(0, 118, 193, 1)', textAlign: 'center', fontWeight:'bold'}}>Voltar</Text>
              </TouchableOpacity>
            </View>
          </>
        }
      </View>
        :
        <Progress.CircleSnail
          style={styles.progressCircle}
          color={['#31788A']}
          size={50}
          indeterminate={true}
        />
      }
    </>
  );
}

const styles = StyleSheet.create({
  input: {
    height: 50,
    width: '92%',
    borderWidth: 1,
    padding: 15,
    borderColor:'#fff',
    borderRadius: 30,
    elevation:2,
    backgroundColor: '#fff',
    color: '#000',
},
inputLabel: {
  fontSize: 16,
  marginTop: 22,
  marginBottom: 16,
},
viaturaCard: {
  flex: 1,
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-around',
  borderBottomWidth: 1,
  borderBottomColor:'rgba(226, 226, 226, 1)',
  backgroundColor: '#fff',
  color: '#000',
  width: '100%',
  marginTop: 15,
  borderRadius: 20,
  //elevation: 1,
  height: 97,
},
viaturaImagem: {
  width: '100%',
  height: '100%',
},
prosseguir: {
  backgroundColor: 'rgba(0, 118, 193, 1)',
  padding: 20,
  borderRadius: 30,
  width: '100%',
},
voltar: {
  backgroundColor: '#fff',
  padding: 20,
  borderRadius: 30,
  width: '100%',
  marginTop:'5%',
  borderColor:'rgba(0, 118, 193, 1)',
  borderWidth: 2
},
progressCircle: {
  alignSelf: 'center',
},
})

export default PartidaViatura;