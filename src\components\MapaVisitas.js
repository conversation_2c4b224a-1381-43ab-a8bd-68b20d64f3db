import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ScrollView
} from 'react-native';
import MapboxGL from "@react-native-mapbox-gl/maps";
import { Dimensions } from 'react-native'
import { useLocation } from '../context/LocationContext';
import { useAlertContext } from '../context/AlertDadosContext';
import * as Progress from 'react-native-progress';
import { Icon } from 'react-native-elements'
import { Button } from 'react-native-elements/dist/buttons/Button';
import BotaoEnviarViatura from './BotaoEnviarViatura';
import BotaoSelectViatura from './BotaoSelectViatura';
import axios from 'axios';
import { URL_API } from '../services/urls';

const window = Dimensions.get('window');
const width = window.width;
const height = window.height;

MapboxGL.setAccessToken("pk.eyJ1Ijoiam9pY3lhbGJ1cXVlcnF1ZSIsImEiOiJja3JueGdla2IybGo0Mm9wNnB3eW1qdzc2In0.ketsp_41i3caR8Y4rw_ZkQ");

const isLatitudeAndLongitudeValid = (latitude, longitude) => Number.isFinite(Number(longitude)) && Number.isFinite(Number(latitude)) && Number(latitude) >= -90 && Number(latitude) <= 90

function MapaVisitas(props) {

  const {location} = useLocation();
  const {datas} = useAlertContext();
  const [indice, setIndice] = useState(datas.length-1);
  const [view, setView] = useState(false);
  const [loading, setLoading] = useState(false);
  const data = [...datas]
  const dataReverse = data.reverse();
  const [fazendas, setFazendas] = useState([])


  console.log('lat, lng',location.latitude,location.longitude);

  useEffect(()=>{
    if(location.latitude != undefined && location.longitude != undefined){
      setView(true)
    }
  }, [location.latitude])

  return (
    <>
        <View style={styles.main}>
          <View style={styles.mapa}>
              {view 
              ? 
              <MapboxGL.MapView
                  styleURL={MapboxGL.StyleURL.Street}
                  zoomLevel={10}
                  centerCoordinate={[location.longitude, location.latitude]}
                  style={{flex: 1}}
                  logoEnabled={false}
              >
                  <MapboxGL.Camera
                  zoomLevel={10}
                  centerCoordinate={[location.longitude, location.latitude]}
                  animationMode={'flyTo'}
                  animationDuration={0}
                  >
                  </MapboxGL.Camera>
                  
                  {props.fazendas.map((data)=>
                    isLatitudeAndLongitudeValid(data.latitude, data.longitude)
                    &&
                    <MapboxGL.PointAnnotation
                      id={JSON.stringify(data.id_fzd)}
                      coordinate={[Number(data.longitude), Number(data.latitude)]}
                    >
                      <MapboxGL.Callout title={data.nome_da_fazenda} />
                    </MapboxGL.PointAnnotation>
                  )}
                  <MapboxGL.UserLocation />
              </MapboxGL.MapView>
              :
              <Progress.CircleSnail
                  style={styles.progressCircle}
                  color={['#31788A']}
                  size={50}
                  indeterminate={true}
              />
              }
          </View>
        </View>
      
    </>
  );
}

const styles = StyleSheet.create({
  main: {
    flex:1,
    backgroundColor: '#fff'
  },
  titulo:{
    textAlign:'left',
    marginLeft: '5%',
    marginTop:'4%',
    fontSize: 15,
    marginBottom:'5%'
  },
  mapa:{
    backgroundColor: '#fff',
    width: '100%',
    height:350,
    borderRadius: 30,
    overflow: 'hidden',
  },
  mapaContent:{
    
  },
  progressCircle: {
    alignSelf: 'center',
  },
  
});

export default MapaVisitas;