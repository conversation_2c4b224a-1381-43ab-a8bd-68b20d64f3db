import React, { useState, useContext, useEffect } from 'react';
import {useSocketContext} from './socketContext';
import { useImmer } from "use-immer";
import axios from 'axios';
import { URL_API} from '../services/urls';
import {useNetInfo} from '@react-native-community/netinfo';
import { Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useVisitas } from './ContextApi';
import AsyncStorage from '@react-native-community/async-storage';

const FetchValidacaoVisita = React.createContext();

export function useFetchValidacaoVisita() {
  return useContext(FetchValidacaoVisita);
}  

export function FetchValidacaoProvider({ children }) {

    const netInfo = useNetInfo();
    const navigation = useNavigation();
    const {rota, setRota} = useVisitas();
    const [error, setError] = useState(0);
    const [disable, setDisable] = useState(false);

    useEffect(()=>{

        handleFetchValidacaoVisita();
        getDataValidacao();

        const interval = setInterval(() => {
            handleFetchValidacaoVisita();
            getDataValidacao();
        }, 10000);
        return () => clearInterval(interval);

    },[netInfo.isConnected]);

    const getDataValidacao = async () => {
        if(netInfo.isConnected == true){
            await AsyncStorage.getItem('@dataValidacao')
            .then(res => {
                if(JSON.parse(res).length > 0){
                    setDisable(true);
                }
                else{
                    setDisable(false);
                }
            })
            .catch(res=> console.log(res));
        }
        else if(netInfo.isConnected == false){
            setDisable(true);
        }
    }

    async function handleFetchValidacaoVisita(){

        await AsyncStorage.getItem('@dataValidacao')
        .then(res => {
            data = JSON.parse(res);
            if(data.length > 0 && netInfo.isConnected){
                for(let i=0; i < data.length; i++){
                  handleValidar(data[i], i, 1);
                }
                // if(error > 0){
                //     Alert.alert('Aviso', `${data.length - error} visitas foram validadas e ${error} deram erro, tente novamente mais tarde!`);
                // }
                // else{
                //     Alert.alert('Sucesso', `${data.length} visita(s) validada(s)!`)
                // }
            }
        })
        .catch( err => console.log('errooooo', err))
    }

    function handleValidar(dataValidacao, index, dadosDoAsyncStorage){

        axios.post(URL_API+'salvar_validacao', dataValidacao)
        .then(res=>{
            
            refreshRota(dataValidacao);

            if(dadosDoAsyncStorage !== 1){ // se o handleValidar não foi pelo handleFetchValidacaoVisita()
                Alert.alert('Sucesso', 'Visita validada com sucesso');
            }
            else{
                removeData(index);
            }
            navigation.navigate('Policial');
        })
        .catch(res=> {
        
            console.log('errorEnviarData', res);

            if(!netInfo.isConnected && dadosDoAsyncStorage !== 1){
                setData(dataValidacao)
                Alert.alert('Atenção', 'A visita será validada assim que a internet do dispositivo estiver disponível!');
                navigation.navigate('Policial');
            }

            if(netInfo.isConnected){
                Alert.alert('Atenção', 'Erro na validação da visita');
            }
        })  
    }

    async function setData(dataValidacao){

        await AsyncStorage.getItem('@dataValidacao')
        .then(async data =>{
            if(data == null){
                await AsyncStorage.setItem('@dataValidacao', JSON.stringify([dataValidacao]));
            }
            else{
                let dataParse = JSON.parse(data);
                dataParse = dataParse.concat(dataValidacao);
                AsyncStorage.setItem('@dataValidacao', JSON.stringify(dataParse));
            }
        })
        .catch(err=> console.log('erroSetData',err))
    }

    async function removeData(index){

        await AsyncStorage.getItem('@dataValidacao')
        .then(async res =>{
            let data = JSON.parse(res);
            
            data.splice(index,1);
            console.log('data',data)
            await AsyncStorage.setItem('@dataValidacao', JSON.stringify(data));

        })
        .catch(err => console.log('errRemoveData', err))
    }

    const refreshRota = (dataValidacao) => {
        const fazenda = rota.fazendas.find(fzd => fzd.id_fzd === dataValidacao.fzd_id);
        const visitadas = rota.fazendas_visitadas;

        if(fazenda){
            visitadas.push({fzd_id:fazenda.id_fzd})

            const newRota = {
                ...rota,
                fazendas_visitadas: visitadas
            }
    
            setRota(newRota);
        }
        
    }

  return (
    <FetchValidacaoVisita.Provider value={{handleValidar, disable}}>
      {children}
    </FetchValidacaoVisita.Provider>
  );
}