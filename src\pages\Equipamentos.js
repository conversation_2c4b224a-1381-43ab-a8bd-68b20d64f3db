import React, { useState, useContext, useRef, useEffect } from 'react';
import { AppState, StyleSheet } from "react-native";
import {
  View,
  Text,
  StatusBar,
  ScrollView,
  TouchableOpacity,
  TextInput,
  VirtualizedList
} from 'react-native';
import axios from 'axios'
import { useNavigation, useRoute } from '@react-navigation/native';
import MapaFazendas from '../components/MapaFazendas';
import { Icon } from 'react-native-elements'
import Header from '../components/Header'
import { Dimensions } from 'react-native'
import ListaFazendas from '../components/ListaFazendas';
import { URL_API } from '../services/urls'
import CardEquipamento from '../components/CardEquipamento'
const window = Dimensions.get('window');
const width = window.width;
const height = window.height;


function Equipamentos() {

  const [devices, setDevices] = useState()

  const navigation = useNavigation();

  const route = useRoute();

  const id = route.params.id

  const [loading, setLoading] = useState(true)

  useEffect(() => {
    axios.post(URL_API + "equipamentos_por_fzd_id", {
      fzd_id: id
    })
      .then(res => {
        console.log(res.data.devices)
        setDevices(res.data.devices)
        setLoading(false)
      })
      .catch(err => {
        console.log(err)
      })
  }, [])

  const teste = () => {
    console.log(devices.items[0].status)
  }

  return (
    <View style={styles.main}>
      <StatusBar barStyle={'light-content'} />
      <ScrollView contentInsetAdjustmentBehavior="automatic">
        <Header subtitle="Proprietário" />
        <View style={styles.blueCard}>

        </View>

        <View>
          
          {devices && devices.items.map((item) => {
            return <CardEquipamento 
              type={item.type} 
              description={item.description}
              armed={item.status.armed}
              id={item.id_number}/>
          })}  

        </View>
      </ScrollView>
    </View>
  );
}
const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor: '#fff'
  },
  blueCard: {
    flex: 1,
    width: '100%',
    height: 100,
    backgroundColor: 'rgba(0, 118, 193, 1)',
    padding: 5
  },
  title: {
    color: '#fff',
    textAlign: 'left',
    marginLeft: '5%',
    fontSize: 20,
    fontWeight: 'bold'
  },
  info: {
    textAlign: 'center',
    color: 'gray',
    width: '70%',
    alignSelf: 'center'
  },
  input: {
    height: 40,
    margin: 12,
    padding: 10,
    elevation: 2,
    borderRadius: 30,
    backgroundColor: '#fff',
    color: 'gray',
  },
  mainCard: {
    paddingTop: 10,
  },
  card: {
    backgroundColor: '#fff',
    width: '89%',
    flex: 1,
    alignSelf: 'center',
    elevation: 1,
    borderRadius: 30,
    marginTop: '-25%',
    padding: 15,
  },
  texts: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-around'
  },
  nome: {
    color: 'gray'
  },
  nomeContent: {
    fontSize: 17
  },
  detalhe: {
    flex: 1,
  },
  detalheText: {
    color: 'rgba(0, 118, 193, 1)',
    alignSelf: 'center',
    marginTop: '2%'
  }
});
export default Equipamentos;