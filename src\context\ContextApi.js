import React from 'react';
import { createContext, useContext } from "react";
import { useState } from "react";

const ContextApi = React.createContext();

export function useVisitas() {
  return useContext(ContextApi);
}

export const VisitasProvider = ({children}) => {
  const [rota, setRota]= useState({});
  return(
    <ContextApi.Provider value={{rota, setRota}}>
      {children}
    </ContextApi.Provider>
  )
}