/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 * @flow strict-local
 */

import React from 'react'
import { LocationProvider } from './context/LocationContext'
import { UserProvider } from './context/AuthContext'
import AuthRoutes from './RoutesAuth'
import {SocketProvider} from './context/socketContext'
import { PermissionProvider } from './context/LocationPermissionContext'
import {BackgroundProvider} from './context/BackgroundContext';
import { NavigationContainer } from "@react-navigation/native";
import { AlertProvider } from './context/AlertDadosContext';
import {VisitasProvider} from './context/ContextApi';
import { FetchValidacaoProvider } from './context/fetchValidacaoVisita'

const App = () => {
  return (
    <NavigationContainer>
      <SocketProvider>
        <UserProvider>
          <LocationProvider>
            <PermissionProvider>
              <AlertProvider>
                <VisitasProvider>
                  <FetchValidacaoProvider>
                    <AuthRoutes/>
                  </FetchValidacaoProvider>
                </VisitasProvider>
              </AlertProvider>
            </PermissionProvider>
          </LocationProvider>
        </UserProvider>      
      </SocketProvider>
    </NavigationContainer>
  );
};

export default App;