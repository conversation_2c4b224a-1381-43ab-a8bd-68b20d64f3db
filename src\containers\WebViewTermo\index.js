import React, {useState, useContext, useRef, useEffect} from 'react';
import {View, Text, StatusBar, ScrollView, Button, StyleSheet, TouchableOpacity } from 'react-native';
import { WebView } from 'react-native-webview';
import axios from 'axios'
import {useUserContext} from '../../context/AuthContext'
import { URL_API } from '../../services/urls';
import AsyncStorage from '@react-native-community/async-storage';

function WebViewTermo({navigation}) {

    const {handleUserLogin, loading, bloqueado, user} = useUserContext();

    const aceitarTermo = () => {
        console.log(user)

        axios.put(URL_API + "aceitar_termos", {
            id_usuario: user.ID_USUARIO
        })
            .then(res => {
                navigation.reset({
                    index: 0,
                    routes: [{ name: 'Login' }],
                });
            })
            .catch(err => {
            })
    }

    const recusarTermo = async () => {
        
        await AsyncStorage.removeItem('@Id');
        await AsyncStorage.removeItem('@Identificacao');
        await AsyncStorage.removeItem('@Viatura');
        await AsyncStorage.removeItem("@Nome");
        await AsyncStorage.removeItem("@Usuario");
        navigation.reset({
            index: 0,
            routes: [{ name: 'Login' }],
        });
    }
    return(
        <View style={{flex: 1}}>
            <WebView
            source={{
            uri: 'https://aiba-web-integracao.herokuapp.com/terms'
            }}
            style={{ marginTop: 10 }}
            />
            <View style={styles.fixToText}>
                <Button 
                    title="Recusar termos"
                    onPress={recusarTermo}/>
                <Button 
                    title="Aceitar termos"
                    onPress={aceitarTermo}/>
            </View>
        </View>
    )
}

const styles = StyleSheet.create({
    main: {
        flex: 1,
        alignItems: 'center',
    },
    button: {
      padding: 15,
      borderRadius: 30,
      marginTop: '2%',
      width: '47%',
      backgroundColor: '#fff',
      elevation: 1,
    },
    textButton: {
        color: '#000',
        fontSize: 15,
        textAlign: 'left',
    },
    fixToText: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
  });

export default WebViewTermo