
 import { useNavigation } from '@react-navigation/native';
import axios from 'axios';
 import React,{useState,useEffect} from 'react';
 import {
   Alert,
   Linking,
   SafeAreaView,
   ScrollView,
   StatusBar,
   StyleSheet,
   Text,
   TextInput,
   TouchableOpacity,
   View,
 } from 'react-native';
import {Modal} from 'react-native';
import { URL_API } from '../services/urls';
import moment from 'moment';


function DetalheOcorrencia ({route}) {

  const navigation = useNavigation();
  const[modalVisible, setModalVisible] = useState(false);
  const[detalheOcorrencia, setDetalheOcorrencia] = useState({});

  const[justificativa, setJustificativa] = useState();

  const fetchDetalheOcorrencia = async ()=> {

    const {
      id,
    } = route.params.item

    const detalheOcorrencia = await axios.get(URL_API + `detalhe_ocorrencia?id_ocorrencia=${id}`)
    console.log("Detalhe Cocorrencia:", detalheOcorrencia)

    setDetalheOcorrencia(detalheOcorrencia.data);
  }

  const postConcluirOcorrencia = async () =>{
    try{
      let response = await axios.post(URL_API + 'concluir_ocorrencia',{
        id_ocorrencia: route.params.item.id,
        justificativa: justificativa,
      })
      //console.log('Post sucesso', response)
      Alert.alert(response.data.message)
    } 
    catch(error){
      //console.log("Erro:", error)
      Alert.alert(error.response.data.errmessage);
    }
  }
 
  useEffect(() => {
    fetchDetalheOcorrencia();
  }, [])

  const plotRoute = () => {
    Linking.openURL('https://www.google.com/maps/dir/?api=1&destination=' + route.params.item.fazendaLatitude + ',' + route.params.item.fazendaLongitude + '')
  }

  return (
    <SafeAreaView style={styles.main}>
      <StatusBar />
      <ScrollView>
        <View>
          <Text style={styles.info}>Confira os Detalhes da Ocorrência</Text>
        </View>
              <View style={styles.card}>
                <View style={styles.option}>
                  <Text>#{detalheOcorrencia.id}</Text>
                  <Text>Fazenda: {route.params.item.fazendaNome}</Text> 
                  <Text>Proprietário: {route.params.item.proprietarioNome}</Text>
                  <Text>Status: {detalheOcorrencia.status}</Text>
                  <Text>{moment(detalheOcorrencia.created_at).format('DD/MM/YYYY hh:mm:ss')}</Text>
                  <Text style={styles.position}>Descrição:</Text>
                  <Text></Text>
                  <Text>{detalheOcorrencia.descricao}</Text>
                </View>
              </View>
              <TouchableOpacity  style={styles.voltar} onPress={() => navigation.navigate('ocorrencias')}>
                <Text style={styles.textVoltar} >Voltar</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => {plotRoute()}} style={styles.voltar}>
                <Text style={styles.textVoltar} >Traçar Rota</Text>
              </TouchableOpacity>
              <TouchableOpacity  style={styles.encerrarOcorrencia} onPress={() => setModalVisible(true)}>
                <Text style={styles.textEncerrarOcorrencia} >Encerrar a Ocorrência</Text>
              </TouchableOpacity>
              <Modal 
                visible={modalVisible}
                animationType="fade"
                transparent={true}
                onRequestClose={()=> setModalVisible(false)}
                >
                <View style={styles.box}>
                  <View style={styles.boxBody}>
                    <View>
                      <Text style={styles.info}>Escreva sua Justificativa</Text>
                    </View>

                    <View style={styles.cardModal}>  
                      <TextInput
                        style={styles.input}
                        onChangeText={(e) => setJustificativa(e)}
                        value={justificativa}
                        placeholder="Digite aqui"
                        placeholderTextColor='gray'
                        maxLength={255}
                        numberOfLines={6}
                        multiline
                      />
                    </View>

                    <TouchableOpacity  style={styles.voltar}>
                      <Text style={styles.textVoltar} onPress={() => setModalVisible(false)} >Cancelar</Text>
                    </TouchableOpacity>

                    <TouchableOpacity 
                      style={styles.encerrarOcorrencia}
                      onPress={
                        () => {setModalVisible(false);
                        postConcluirOcorrencia()}}
                    >
                      <Text style={styles.textEncerrarOcorrencia}>Enviar Justificativa</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </Modal>
                
      </ScrollView>
    </SafeAreaView>
  );
}

  const styles = StyleSheet.create({
   
    main:{
      flex:1,
      backgroundColor: '#fff'
    },

    info:{
        marginTop:'5%',
        marginBottom:'5%',
        textAlign: 'center',
        color: 'gray',
        width: '70%',
        alignSelf: 'center'
    },
    card:{
      backgroundColor: '#fff',
      width: '100%',
      height:'auto',
      borderRadius: 30,
      elevation: 1,
      marginBottom: '10%',
    },
    cardModal:{
      backgroundColor: '#fff',
      width: '100%',
      height:'auto',
      borderRadius: 30,
      elevation: 0,
      marginBottom: '10%',
    },
    option:{
      // backgroundColor:'#ddd',
      marginLeft:'5%',
      marginRight:'5%',
      marginTop:'5%',
      marginBottom:'5%',
      padding:10,
      borderRadius:5,
    },
    position:{
      alignSelf: 'center',
      marginTop:'5%',
    },
    voltar: {
      backgroundColor: '#fff',
      padding: 20,
      borderRadius: 30,
      width: '90%',
      borderColor:'rgba(0, 118, 193, 1)',
      borderWidth:2,
      marginBottom: '10%',
      alignSelf: 'center'
    },
    textVoltar:{
      color:'rgba(0, 118, 193, 1)', 
      textAlign: 'center', 
      fontWeight:'bold',
    },
    encerrarOcorrencia:{
      backgroundColor: 'rgba(0, 118, 193, 1)',
      padding: 20,
      borderRadius: 30,
      width: '90%',
      borderColor:'rgba(0, 118, 193, 1)',
      borderWidth:2,
      alignSelf: 'center'
    },
    textEncerrarOcorrencia:{
      color:'#fff', 
      textAlign: 'center', 
      fontWeight:'bold',
    },
    box:{
     width:'100%',
     height:'100%',
     backgroundColor:'rgba(0,0,0,0.5)',
     justifyContent: 'center',
     alignItems: 'center',
    },
    boxBody:{
      width:'80%',
      height:'80%',
      backgroundColor:'#fff',
      borderRadius: 30,
     
    },
    input: {
      width: '92%',
      borderWidth: 1,
      padding: 10,
      borderColor: '#fff',
      borderRadius: 30,
      elevation: 2,
      backgroundColor: '#fff',
      color: '#000',
      alignSelf:'center',
  },
});
  
 
 export default DetalheOcorrencia;
 