import { URL_API } from "./services/urls";

export const saveGeolocation = json => {
  const URL = URL_API+"app_aiba"

  const data = JSON.stringify({
    "latitude": json.latitude,
    "longitude": json.longitude,
    "userId": json.userId
  });

  return fetch(URL, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: data
  }).then(res => res.json()) 
    .then(res => {
      // console.log(`Geolocation has been sent at ${new Date().toISOString()}: `, data, json.userId)
    }).catch(err => {
    console.error(JSON.stringify(err))
  })
}