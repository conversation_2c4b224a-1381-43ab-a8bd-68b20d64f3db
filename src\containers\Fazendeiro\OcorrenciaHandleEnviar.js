import {Alert} from 'react-native';
import { URL_API } from '../../services/urls';


const OcorrenciaHandleEnviar = async (axios, navigation, motivo, descricao, delegacia, idProprietario, id_fazenda) => {
  if (motivo === '' || motivo === undefined){
      Alert.alert('Selecione o motivo da ocorrência')
      return
  }
      
  if (descricao === '' || descricao === undefined){
      Alert.alert('A Descrição deve ser preenchida')
      return
  } 

  return axios.post(URL_API + 'registrar_ocorrencia', {
      id_prop: idProprietario,
      id_fzd: id_fazenda,
      id_motivo: motivo,
      descricao: descricao,
      registro_delegacia: delegacia
  })
      .then(res => {             
          Alert.alert('Ocorrência Aberta');
          navigation.navigate('Fazendeiro') 
          return res;
      })
      .catch(err =>{
          Alert.alert('Houve um erro ao abrir a ocorrência, tente novamente')
          return err;
      })
}

export default OcorrenciaHandleEnviar;